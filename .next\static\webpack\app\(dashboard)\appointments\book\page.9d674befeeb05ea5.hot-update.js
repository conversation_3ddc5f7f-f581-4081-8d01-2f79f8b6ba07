"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* harmony import */ var _lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/dateValidation */ \"(app-pages-browser)/./src/lib/utils/dateValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * CRITICAL FEATURE: UI-level date blocking validation\n   * Validates which dates should be blocked based on 4-hour advance booking rule\n   * Prevents user confusion by showing blocked dates as disabled\n   */ const dateValidationResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (weekData.length === 0) return {};\n        const dates = weekData.map((day)=>day.date);\n        // Create mock time slots for validation (typical business hours)\n        const availableSlotsByDate = {};\n        weekData.forEach((day)=>{\n            if (day.availabilityLevel !== \"none\") {\n                // Generate typical business hours for validation\n                availableSlotsByDate[day.date] = [\n                    {\n                        date: day.date,\n                        time: \"08:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"10:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"11:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"14:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"15:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"16:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"17:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"18:00\",\n                        available: true\n                    }\n                ];\n            } else {\n                availableSlotsByDate[day.date] = [];\n            }\n        });\n        console.log(\"=== DEBUG DATE BLOCKING VALIDATION ===\");\n        console.log(\"Validating dates:\", dates);\n        console.log(\"Available slots by date:\", availableSlotsByDate);\n        const validationResults = (0,_lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__.validateMultipleDates)(dates, availableSlotsByDate);\n        console.log(\"Validation results:\", validationResults);\n        console.log(\"========================================\");\n        return validationResults;\n    }, [\n        weekData\n    ]);\n    /**\n   * Enhanced week data with blocking information\n   */ const enhancedWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return weekData.map((day)=>{\n            const validation = dateValidationResults[day.date];\n            const isBlocked = validation && !validation.isValid;\n            return {\n                ...day,\n                isBlocked,\n                blockReason: validation === null || validation === void 0 ? void 0 : validation.reason,\n                validationResult: validation\n            };\n        });\n    }, [\n        weekData,\n        dateValidationResults\n    ]);\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha con validación de bloqueo\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE + BLOCKING) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FEATURE: Check if date is blocked by UI validation\n        const validation = dateValidationResults[date];\n        const isBlocked = validation && !validation.isValid;\n        console.log(\"Validaci\\xf3n de bloqueo:\");\n        console.log(\"  - validation:\", validation);\n        console.log(\"  - isBlocked:\", isBlocked);\n        console.log(\"  - blockReason:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n        if (isBlocked) {\n            console.log(\"\\uD83D\\uDEAB FECHA BLOQUEADA - No se permite selecci\\xf3n\");\n            console.log(\"Raz\\xf3n:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n            console.log(\"=======================================\");\n            // Show user feedback (could be enhanced with toast notification)\n            alert(\"Esta fecha no est\\xe1 disponible: \".concat(validation === null || validation === void 0 ? void 0 : validation.reason));\n            return;\n        }\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic using timezone-safe objects\n        const utcDateStringFromUTC = dateObjUTC.toISOString().split(\"T\")[0];\n        const utcDateStringFromLocal = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateStringFromUTC;\n        console.log(\"Comparaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString (from UTC obj):\", utcDateStringFromUTC);\n        console.log(\"  - utcDateString (from local obj):\", utcDateStringFromLocal);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"\\xbfDate objects son consistentes?:\", localDateString === date);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"✅ FECHA V\\xc1LIDA - LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 596,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 606,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 619,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 649,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 686,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 699,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 694,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 713,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 712,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 728,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 754,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 755,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 753,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 594,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"14U+tOJsF/03vp4I7+MnLlHkFdQ=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});