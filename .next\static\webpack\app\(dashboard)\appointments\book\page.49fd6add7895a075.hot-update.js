"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* harmony import */ var _lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/dateValidation */ \"(app-pages-browser)/./src/lib/utils/dateValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // CRITICAL FIX: Use timezone-safe date formatting (moved up to be available for logging)\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // CRITICAL FIX: Apply 4-hour rule validation during weekData generation\n                // This ensures that dates that don't meet the 4-hour rule are marked as 'none' from the start\n                let slotsCount = 0;\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                    slotsCount = 0;\n                } else {\n                    // Generate initial slots count\n                    const initialSlotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                    // CRITICAL FIX: Apply 4-hour advance booking rule validation\n                    // Check if this date would have any valid slots after applying the 4-hour rule\n                    const MINIMUM_ADVANCE_HOURS = 4;\n                    const MINIMUM_ADVANCE_MINUTES = MINIMUM_ADVANCE_HOURS * 60;\n                    const now = new Date();\n                    // Generate typical business hours for this date\n                    const businessHours = [\n                        \"08:00\",\n                        \"09:00\",\n                        \"10:00\",\n                        \"11:00\",\n                        \"14:00\",\n                        \"15:00\",\n                        \"16:00\",\n                        \"17:00\",\n                        \"18:00\"\n                    ];\n                    // Count how many slots would be valid after 4-hour rule\n                    let validSlotsCount = 0;\n                    if (initialSlotsCount > 0) {\n                        businessHours.forEach((timeSlot)=>{\n                            const [hours, minutes] = timeSlot.split(\":\").map(Number);\n                            const slotDateTime = new Date(date.getFullYear(), date.getMonth(), date.getDate(), hours, minutes);\n                            const timeDifferenceMs = slotDateTime.getTime() - now.getTime();\n                            const timeDifferenceMinutes = Math.floor(timeDifferenceMs / (1000 * 60));\n                            if (timeDifferenceMinutes >= MINIMUM_ADVANCE_MINUTES) {\n                                validSlotsCount++;\n                            }\n                        });\n                        // Only count slots that would actually be available after 4-hour rule\n                        slotsCount = Math.min(initialSlotsCount, validSlotsCount);\n                    }\n                    // Set availability level based on valid slots count\n                    if (slotsCount === 0) {\n                        availabilityLevel = \"none\";\n                    } else if (slotsCount <= 2) {\n                        availabilityLevel = \"low\";\n                    } else if (slotsCount <= 5) {\n                        availabilityLevel = \"medium\";\n                    } else {\n                        availabilityLevel = \"high\";\n                    }\n                    console.log(\"\\uD83D\\uDD0D VALIDACI\\xd3N 4H INTEGRADA - \".concat(finalDateString, \":\"), {\n                        initialSlotsCount,\n                        validSlotsCount,\n                        finalSlotsCount: slotsCount,\n                        availabilityLevel,\n                        isWeekend,\n                        timeDifferenceToFirstSlot: businessHours.length > 0 ? Math.floor((new Date(date.getFullYear(), date.getMonth(), date.getDate(), 8, 0).getTime() - now.getTime()) / (1000 * 60)) : \"N/A\"\n                    });\n                }\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * CRITICAL FEATURE: UI-level date blocking validation\n   * Validates which dates should be blocked based on 4-hour advance booking rule\n   * Prevents user confusion by showing blocked dates as disabled\n   */ const dateValidationResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (weekData.length === 0) return {};\n        const dates = weekData.map((day)=>day.date);\n        // Create mock time slots for validation (typical business hours)\n        const availableSlotsByDate = {};\n        weekData.forEach((day)=>{\n            if (day.availabilityLevel !== \"none\") {\n                // Generate typical business hours for validation\n                availableSlotsByDate[day.date] = [\n                    {\n                        date: day.date,\n                        time: \"08:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"10:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"11:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"14:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"15:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"16:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"17:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"18:00\",\n                        available: true\n                    }\n                ];\n            } else {\n                availableSlotsByDate[day.date] = [];\n            }\n        });\n        console.log(\"=== DEBUG DATE BLOCKING VALIDATION ===\");\n        console.log(\"Validating dates:\", dates);\n        console.log(\"Available slots by date:\", availableSlotsByDate);\n        const validationResults = (0,_lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__.validateMultipleDates)(dates, availableSlotsByDate);\n        console.log(\"Validation results:\", validationResults);\n        console.log(\"========================================\");\n        return validationResults;\n    }, [\n        weekData\n    ]);\n    /**\n   * Enhanced week data with blocking information\n   */ const enhancedWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return weekData.map((day)=>{\n            const validation = dateValidationResults[day.date];\n            const isBlocked = validation && !validation.isValid;\n            return {\n                ...day,\n                isBlocked,\n                blockReason: validation === null || validation === void 0 ? void 0 : validation.reason,\n                validationResult: validation\n            };\n        });\n    }, [\n        weekData,\n        dateValidationResults\n    ]);\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha con validación de bloqueo\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE + BLOCKING) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FEATURE: Check if date is blocked by UI validation\n        const validation = dateValidationResults[date];\n        const isBlocked = validation && !validation.isValid;\n        console.log(\"Validaci\\xf3n de bloqueo:\");\n        console.log(\"  - validation:\", validation);\n        console.log(\"  - isBlocked:\", isBlocked);\n        console.log(\"  - blockReason:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n        if (isBlocked) {\n            console.log(\"\\uD83D\\uDEAB FECHA BLOQUEADA - No se permite selecci\\xf3n\");\n            console.log(\"Raz\\xf3n:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n            console.log(\"=======================================\");\n            // Show user feedback (could be enhanced with toast notification)\n            alert(\"Esta fecha no est\\xe1 disponible: \".concat(validation === null || validation === void 0 ? void 0 : validation.reason));\n            return;\n        }\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic using timezone-safe objects\n        const utcDateStringFromUTC = dateObjUTC.toISOString().split(\"T\")[0];\n        const utcDateStringFromLocal = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateStringFromUTC;\n        console.log(\"Comparaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString (from UTC obj):\", utcDateStringFromUTC);\n        console.log(\"  - utcDateString (from local obj):\", utcDateStringFromLocal);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"\\xbfDate objects son consistentes?:\", localDateString === date);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"✅ FECHA V\\xc1LIDA - LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 649,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 648,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 661,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 691,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 700,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 728,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 741,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 742,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 736,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && enhancedWeekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: enhancedWeekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount,\n                            isBlocked: day.isBlocked,\n                            blockReason: day.blockReason\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 755,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 754,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 773,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 783,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 774,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 772,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 798,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 799,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 800,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 801,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 797,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 636,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"14U+tOJsF/03vp4I7+MnLlHkFdQ=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});