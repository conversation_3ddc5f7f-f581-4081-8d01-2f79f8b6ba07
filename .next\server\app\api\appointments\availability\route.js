"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/appointments/availability/route";
exports.ids = ["app/api/appointments/availability/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Favailability%2Froute&page=%2Fapi%2Fappointments%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Favailability%2Froute&page=%2Fapi%2Fappointments%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_appointments_availability_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/appointments/availability/route.ts */ \"(rsc)/./src/app/api/appointments/availability/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/appointments/availability/route\",\n        pathname: \"/api/appointments/availability\",\n        filename: \"route\",\n        bundlePath: \"app/api/appointments/availability/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\api\\\\appointments\\\\availability\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_appointments_availability_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/appointments/availability/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Favailability%2Froute&page=%2Fapi%2Fappointments%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/appointments/availability/route.ts":
/*!********************************************************!*\
  !*** ./src/app/api/appointments/availability/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * API Route: /api/appointments/availability\n * \n * Endpoint para obtener disponibilidad de doctores por rango de fechas\n * Requerido por WeeklyAvailabilitySelector y SmartSuggestionsEngine\n * \n * Funcionalidades:\n * - Consulta disponibilidad por rango de fechas\n * - Filtrado por servicio, doctor y ubicación\n * - Soporte multi-tenant con organizationId\n * - Formato optimizado para WeeklyAvailabilitySelector\n * - Integración con SmartSuggestionsEngine\n * \n * <AUTHOR> MVP Team - Critical API Fix\n * @version 1.0.0\n */ \n\n// Initialize Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://fjvletqwwmxusgthwphr.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n/**\n * GET /api/appointments/availability\n * \n * Query Parameters:\n * - organizationId (required): ID de la organización\n * - startDate (required): Fecha de inicio (YYYY-MM-DD)\n * - endDate (required): Fecha de fin (YYYY-MM-DD)\n * - serviceId (optional): ID del servicio\n * - doctorId (optional): ID del doctor\n * - locationId (optional): ID de la ubicación\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Validar parámetros requeridos\n        const organizationId = searchParams.get(\"organizationId\");\n        const startDate = searchParams.get(\"startDate\");\n        const endDate = searchParams.get(\"endDate\");\n        if (!organizationId || !startDate || !endDate) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"organizationId, startDate y endDate son requeridos\"\n            }, {\n                status: 400\n            });\n        }\n        // Parámetros opcionales\n        const serviceId = searchParams.get(\"serviceId\");\n        const doctorId = searchParams.get(\"doctorId\");\n        const locationId = searchParams.get(\"locationId\");\n        // Validar formato de fechas\n        const startDateObj = new Date(startDate);\n        const endDateObj = new Date(endDate);\n        if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Formato de fecha inv\\xe1lido. Use YYYY-MM-DD\"\n            }, {\n                status: 400\n            });\n        }\n        if (startDateObj > endDateObj) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"startDate debe ser anterior o igual a endDate\"\n            }, {\n                status: 400\n            });\n        }\n        // Construir consulta base para doctor_availability\n        // NOTA: La tabla usa day_of_week, no date específica\n        let query = supabase.from(\"doctor_availability\").select(`\n        *,\n        doctor:profiles!doctor_availability_doctor_id_fkey(\n          id,\n          first_name,\n          last_name,\n          role,\n          organization_id\n        ),\n        location:locations(\n          id,\n          name,\n          address\n        )\n      `).eq(\"is_active\", true); // Usar is_active en lugar de is_available\n        // Aplicar filtros opcionales\n        if (doctorId) {\n            query = query.eq(\"doctor_id\", doctorId);\n        }\n        if (locationId) {\n            query = query.eq(\"location_id\", locationId);\n        }\n        // Filtrar por organización a través del doctor\n        query = query.eq(\"doctor.organization_id\", organizationId);\n        // Ejecutar consulta\n        const { data: availabilityData, error: availabilityError } = await query;\n        if (availabilityError) {\n            console.error(\"Error fetching availability:\", availabilityError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Error al consultar disponibilidad\"\n            }, {\n                status: 500\n            });\n        }\n        // Si hay filtro por servicio, obtener doctores que ofrecen ese servicio\n        let validDoctorIds = null;\n        if (serviceId) {\n            // Obtener doctores que ofrecen el servicio, filtrando por organización a través de profiles\n            const { data: doctorServices, error: servicesError } = await supabase.from(\"doctor_services\").select(`\n          doctor_id,\n          doctor:profiles!doctor_services_doctor_id_fkey(\n            organization_id\n          )\n        `).eq(\"service_id\", serviceId);\n            if (servicesError) {\n                console.error(\"Error fetching doctor services:\", servicesError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: \"Error al consultar servicios de doctores\"\n                }, {\n                    status: 500\n                });\n            }\n            // Filtrar por organización\n            validDoctorIds = doctorServices.filter((ds)=>ds.doctor?.organization_id === organizationId).map((ds)=>ds.doctor_id);\n            // Si no hay doctores para este servicio en esta organización, retornar vacío\n            if (validDoctorIds.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {}\n                });\n            }\n        }\n        // Procesar datos de disponibilidad\n        const processedData = {};\n        // Generar todas las fechas en el rango (solo fechas futuras)\n        const today = new Date();\n        today.setHours(0, 0, 0, 0); // Reset time to start of day\n        const currentDate = new Date(Math.max(startDateObj.getTime(), today.getTime()));\n        while(currentDate <= endDateObj){\n            const dateString = currentDate.toISOString().split(\"T\")[0];\n            // Solo incluir fechas futuras o de hoy\n            if (currentDate >= today) {\n                processedData[dateString] = {\n                    slots: [],\n                    totalSlots: 0,\n                    availableSlots: 0\n                };\n            }\n            currentDate.setDate(currentDate.getDate() + 1);\n        }\n        // Procesar cada registro de disponibilidad semanal\n        availabilityData?.forEach((availability)=>{\n            // Filtrar por servicio si es necesario\n            if (validDoctorIds && !validDoctorIds.includes(availability.doctor_id)) {\n                return;\n            }\n            // Convertir day_of_week a fechas específicas en el rango\n            const dayOfWeek = availability.day_of_week; // 0=Sunday, 1=Monday, etc.\n            // Encontrar todas las fechas que coinciden con este día de la semana\n            const matchingDates = [];\n            const tempDate = new Date(startDateObj);\n            while(tempDate <= endDateObj){\n                if (tempDate.getDay() === dayOfWeek) {\n                    matchingDates.push(tempDate.toISOString().split(\"T\")[0]);\n                }\n                tempDate.setDate(tempDate.getDate() + 1);\n            }\n            // Generar slots para cada fecha que coincide\n            matchingDates.forEach((dateString)=>{\n                const startTime = availability.start_time;\n                const endTime = availability.end_time;\n                const slotDuration = 30; // 30 minutos por defecto\n                const slots = generateTimeSlots(startTime, endTime, slotDuration, availability.doctor, availability.doctor_id, serviceId, locationId);\n                if (processedData[dateString]) {\n                    processedData[dateString].slots.push(...slots);\n                    processedData[dateString].totalSlots += slots.length;\n                    processedData[dateString].availableSlots += slots.filter((s)=>s.available).length;\n                }\n            });\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: processedData\n        });\n    } catch (error) {\n        console.error(\"Error in availability API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Error interno del servidor\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Generar slots de tiempo para un rango horario\n */ function generateTimeSlots(startTime, endTime, slotDuration, doctor, doctorId, serviceId, locationId) {\n    const slots = [];\n    try {\n        // Parsear horas de inicio y fin (formato HH:MM:SS o HH:MM)\n        const startTimeParts = startTime.split(\":\");\n        const endTimeParts = endTime.split(\":\");\n        const startHour = parseInt(startTimeParts[0]);\n        const startMinute = parseInt(startTimeParts[1] || \"0\");\n        const endHour = parseInt(endTimeParts[0]);\n        const endMinute = parseInt(endTimeParts[1] || \"0\");\n        const startMinutes = startHour * 60 + startMinute;\n        const endMinutes = endHour * 60 + endMinute;\n        // Validar que el rango sea válido\n        if (startMinutes >= endMinutes) {\n            console.warn(`Invalid time range: ${startTime} - ${endTime}`);\n            return slots;\n        }\n        // Generar slots\n        for(let minutes = startMinutes; minutes < endMinutes; minutes += slotDuration){\n            const hour = Math.floor(minutes / 60);\n            const minute = minutes % 60;\n            const timeString = `${hour.toString().padStart(2, \"0\")}:${minute.toString().padStart(2, \"0\")}`;\n            // Obtener nombre del doctor\n            const doctorName = doctor?.first_name && doctor?.last_name ? `Dr. ${doctor.first_name} ${doctor.last_name}` : doctor?.profiles?.[0] ? `Dr. ${doctor.profiles[0].first_name} ${doctor.profiles[0].last_name}` : \"Doctor\";\n            slots.push({\n                id: `${doctorId}-${timeString}`,\n                time: timeString,\n                doctorId,\n                doctorName,\n                available: true,\n                duration: slotDuration,\n                serviceId: serviceId || undefined,\n                locationId: locationId || undefined\n            });\n        }\n    } catch (error) {\n        console.error(\"Error generating time slots:\", error, {\n            startTime,\n            endTime,\n            doctorId\n        });\n    }\n    return slots;\n}\n/**\n * Validar que el usuario tenga permisos para acceder a la organización\n */ async function validateUserAccess(organizationId, userId) {\n    if (!userId) return false;\n    try {\n        const { data, error } = await supabase.from(\"profiles\").select(\"organization_id\").eq(\"id\", userId).single();\n        if (error || !data) return false;\n        return data.organization_id === organizationId;\n    } catch  {\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/appointments/availability/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Favailability%2Froute&page=%2Fapi%2Fappointments%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();