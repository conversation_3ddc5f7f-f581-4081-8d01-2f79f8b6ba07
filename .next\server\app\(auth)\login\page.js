/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/login/page";
exports.ids = ["app/(auth)/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(rsc)/./src/app/(auth)/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(auth)/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNotificationSystem.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccontexts%5C%5Ctenant-context.tsx%22%2C%22ids%22%3A%5B%22TenantProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNotificationSystem.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccontexts%5C%5Ctenant-context.tsx%22%2C%22ids%22%3A%5B%22TenantProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/NotificationSystem.tsx */ \"(ssr)/./src/components/ui/NotificationSystem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(ssr)/./src/contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/tenant-context.tsx */ \"(ssr)/./src/contexts/tenant-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNotificationSystem.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Ccontexts%5C%5Ctenant-context.tsx%22%2C%22ids%22%3A%5B%22TenantProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(ssr)/./src/app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0p1YW4lMjBQdWxnYXJpbiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNhZ2Vuc2FsdWQtc29ubmV0NCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1QyhhdXRoKSU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUFnSiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnZW5kYWxvLz8yN2RlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxhZ2Vuc2FsdWQtc29ubmV0NFxcXFxzcmNcXFxcYXBwXFxcXChhdXRoKVxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { signIn } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(null);\n        try {\n            const { error } = await signIn(email, password);\n            if (error) {\n                setError(error.message);\n            } else {\n                router.push(\"/dashboard\");\n            }\n        } catch (err) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Iniciar Sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"Accede a tu cuenta de Agendalo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Correo Electr\\xf3nico\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Contrase\\xf1a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            required: true,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Tu contrase\\xf1a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? \"Iniciando sesi\\xf3n...\" : \"Iniciar Sesi\\xf3n\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"\\xbfNo tienes una cuenta?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                        children: \"Reg\\xedstrate aqu\\xed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(auth)/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/NotificationSystem.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/NotificationSystem.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   setGlobalNotificationContext: () => (/* binding */ setGlobalNotificationContext),\n/* harmony export */   showGlobalError: () => (/* binding */ showGlobalError),\n/* harmony export */   showGlobalInfo: () => (/* binding */ showGlobalInfo),\n/* harmony export */   showGlobalNotification: () => (/* binding */ showGlobalNotification),\n/* harmony export */   showGlobalSuccess: () => (/* binding */ showGlobalSuccess),\n/* harmony export */   showGlobalWarning: () => (/* binding */ showGlobalWarning),\n/* harmony export */   useNotificationHelpers: () => (/* binding */ useNotificationHelpers),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ useNotifications,NotificationProvider,setGlobalNotificationContext,showGlobalNotification,showGlobalSuccess,showGlobalError,showGlobalWarning,showGlobalInfo,useNotificationHelpers auto */ \n/**\n * NotificationSystem Component\n * Global notification system for the application\n * Provides toast notifications and alerts\n */ \n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useNotifications() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (!context) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n}\nfunction NotificationProvider({ children }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((notification)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newNotification = {\n            id,\n            duration: 5000,\n            ...notification\n        };\n        setNotifications((prev)=>[\n                ...prev,\n                newNotification\n            ]);\n        // Auto-remove notification after duration (unless persistent)\n        if (!newNotification.persistent && newNotification.duration) {\n            setTimeout(()=>{\n                removeNotification(id);\n            }, newNotification.duration);\n        }\n        return id;\n    }, []);\n    const removeNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== id));\n    }, []);\n    const clearAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setNotifications([]);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: {\n            notifications,\n            addNotification,\n            removeNotification,\n            clearAll\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\nfunction NotificationContainer() {\n    const { notifications, removeNotification } = useNotifications();\n    if (notifications.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full sm:max-w-md md:max-w-lg lg:max-w-sm px-4 sm:px-0 \",\n        role: \"region\",\n        \"aria-label\": \"Notificaciones del sistema\",\n        \"aria-live\": \"polite\",\n        \"aria-atomic\": \"false\",\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationItem, {\n                notification: notification,\n                onClose: ()=>removeNotification(notification.id)\n            }, notification.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\nfunction NotificationItem({ notification, onClose }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Trigger animation\n        const timer = setTimeout(()=>setIsVisible(true), 10);\n        return ()=>clearTimeout(timer);\n    }, []);\n    const handleClose = ()=>{\n        setIsVisible(false);\n        setTimeout(onClose, 150); // Wait for animation\n    };\n    const handleKeyDown = (event)=>{\n        if (event.key === \"Escape\") {\n            handleClose();\n        }\n    };\n    const getIcon = ()=>{\n        const iconProps = {\n            className: \"h-5 w-5\",\n            \"aria-hidden\": true\n        };\n        switch(notification.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    ...iconProps,\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...iconProps,\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    ...iconProps,\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...iconProps,\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...iconProps,\n                    className: \"h-5 w-5 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStyles = ()=>{\n        switch(notification.type){\n            case \"success\":\n                return \"bg-green-50 border-green-200 text-green-800\";\n            case \"error\":\n                return \"bg-red-50 border-red-200 text-red-800\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200 text-yellow-800\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200 text-blue-800\";\n            default:\n                return \"bg-gray-50 border-gray-200 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        ${getStyles()}\n        border rounded-lg shadow-lg p-4 animate-slide-in-up\n        transform transition-all duration-300 ease-out\n        focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2\n        ${isVisible ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"}\n      `,\n        role: \"alert\",\n        \"aria-labelledby\": `notification-title-${notification.id}`,\n        \"aria-describedby\": notification.message ? `notification-message-${notification.id}` : undefined,\n        onKeyDown: handleKeyDown,\n        tabIndex: -1,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            id: `notification-title-${notification.id}`,\n                            className: \"text-sm font-medium break-words\",\n                            children: notification.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        notification.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            id: `notification-message-${notification.id}`,\n                            className: \"text-sm opacity-90 mt-1 break-words\",\n                            children: notification.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this),\n                        notification.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: notification.action.onClick,\n                                className: \" text-sm font-medium underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 \",\n                                children: notification.action.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleClose,\n                        className: \" inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:bg-black hover:bg-opacity-10 transition-colors duration-200 \",\n                        \"aria-label\": `Cerrar notificación: ${notification.title}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\",\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\ui\\\\NotificationSystem.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n// Global notification functions (for use outside React components)\nlet globalNotificationContext = null;\nfunction setGlobalNotificationContext(context) {\n    globalNotificationContext = context;\n}\nfunction showGlobalNotification(notification) {\n    if (globalNotificationContext) {\n        return globalNotificationContext.addNotification(notification);\n    }\n    console.warn(\"Global notification context not available\");\n    return \"\";\n}\nfunction showGlobalSuccess(title, message) {\n    return showGlobalNotification({\n        type: \"success\",\n        title,\n        message\n    });\n}\nfunction showGlobalError(title, message) {\n    return showGlobalNotification({\n        type: \"error\",\n        title,\n        message,\n        duration: 8000\n    });\n}\nfunction showGlobalWarning(title, message) {\n    return showGlobalNotification({\n        type: \"warning\",\n        title,\n        message\n    });\n}\nfunction showGlobalInfo(title, message) {\n    return showGlobalNotification({\n        type: \"info\",\n        title,\n        message\n    });\n}\n/**\n * Hook for standardized notification helpers\n * Provides convenient methods for common notification types\n */ function useNotificationHelpers() {\n    const { addNotification } = useNotifications();\n    const showSuccess = (title, message, action)=>{\n        addNotification({\n            type: \"success\",\n            title,\n            message,\n            action,\n            duration: 4000\n        });\n    };\n    const showError = (title, message, persistent = false)=>{\n        addNotification({\n            type: \"error\",\n            title,\n            message,\n            persistent,\n            duration: persistent ? undefined : 6000\n        });\n    };\n    const showWarning = (title, message)=>{\n        addNotification({\n            type: \"warning\",\n            title,\n            message,\n            duration: 5000\n        });\n    };\n    const showInfo = (title, message)=>{\n        addNotification({\n            type: \"info\",\n            title,\n            message,\n            duration: 4000\n        });\n    };\n    const showAppointmentSuccess = (appointmentDate, doctorName)=>{\n        showSuccess(\"Cita agendada exitosamente\", `Tu cita ha sido confirmada para ${appointmentDate}${doctorName ? ` con ${doctorName}` : \"\"}`, {\n            label: \"Ver cita\",\n            onClick: ()=>window.location.href = \"/appointments\"\n        });\n    };\n    const showAppointmentError = (error)=>{\n        showError(\"Error al agendar cita\", error, true);\n    };\n    const showNetworkError = ()=>{\n        showError(\"Error de conexi\\xf3n\", \"No se pudo conectar al servidor. Verifica tu conexi\\xf3n a internet.\", true);\n    };\n    const showPermissionError = ()=>{\n        showError(\"Acceso denegado\", \"No tienes permisos para realizar esta acci\\xf3n.\");\n    };\n    return {\n        showSuccess,\n        showError,\n        showWarning,\n        showInfo,\n        showAppointmentSuccess,\n        showAppointmentError,\n        showNetworkError,\n        showPermissionError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/NotificationSystem.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session }, error } = await supabase.auth.getSession();\n            if (error) {\n                console.error(\"Error getting session:\", error);\n            } else {\n                setSession(session);\n                setUser(session?.user ?? null);\n                if (session?.user) {\n                    await fetchProfile(session.user.id);\n                }\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchProfile(session.user.id);\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n                return;\n            }\n            setProfile(data);\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        try {\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            return {\n                error\n            };\n        } catch (error) {\n            return {\n                error: error\n            };\n        }\n    };\n    const signUp = async (email, password, userData)=>{\n        try {\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password\n            });\n            if (error) {\n                return {\n                    error\n                };\n            }\n            // Create profile if user was created successfully\n            if (data.user && userData) {\n                const { error: profileError } = await supabase.from(\"profiles\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    role: userData.role || \"patient\",\n                    first_name: userData.first_name,\n                    last_name: userData.last_name,\n                    phone: userData.phone,\n                    organization_id: userData.organization_id\n                });\n                if (profileError) {\n                    console.error(\"Error creating profile:\", profileError);\n                }\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error\n            };\n        }\n    };\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                error: new Error(\"No user logged in\")\n            };\n        }\n        try {\n            const { error } = await supabase.from(\"profiles\").update(updates).eq(\"id\", user.id);\n            if (error) {\n                return {\n                    error\n                };\n            }\n            // Refresh profile data\n            await fetchProfile(user.id);\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error\n            };\n        }\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/tenant-context.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/tenant-context.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./auth-context */ \"(ssr)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction TenantProvider({ children }) {\n    const [organization, setOrganization] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { profile, user } = (0,_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (profile?.organization_id) {\n            fetchOrganization(profile.organization_id);\n        } else {\n            setLoading(false);\n        }\n    }, [\n        profile\n    ]);\n    const fetchOrganization = async (organizationId)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await supabase.from(\"organizations\").select(\"*\").eq(\"id\", organizationId).single();\n            if (error) {\n                console.error(\"Error fetching organization:\", error);\n                setOrganization(null);\n                return;\n            }\n            setOrganization(data);\n        } catch (error) {\n            console.error(\"Error fetching organization:\", error);\n            setOrganization(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const switchOrganization = async (organizationId)=>{\n        if (!user) {\n            throw new Error(\"No user logged in\");\n        }\n        try {\n            // Update user's organization\n            const { error } = await supabase.from(\"profiles\").update({\n                organization_id: organizationId\n            }).eq(\"id\", user.id);\n            if (error) {\n                throw error;\n            }\n            // Fetch new organization\n            await fetchOrganization(organizationId);\n        } catch (error) {\n            console.error(\"Error switching organization:\", error);\n            throw error;\n        }\n    };\n    const refreshOrganization = async ()=>{\n        if (profile?.organization_id) {\n            await fetchOrganization(profile.organization_id);\n        }\n    };\n    const value = {\n        organization,\n        loading,\n        switchOrganization,\n        refreshOrganization\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\contexts\\\\tenant-context.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction useTenant() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (context === undefined) {\n        throw new Error(\"useTenant must be used within a TenantProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/tenant-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n\nconst createClient = ()=>(0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fjvletqwwmxusgthwphr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqdmxldHF3d214dXNndGh3cGhyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDc2MDAsImV4cCI6MjA2Mzc4MzYwMH0.TiU8DGo9kihikfmlk1drLs57tNuOrm_Pgq80yzsWytc\");\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFHN0MsTUFBTUMsZUFBZSxJQUMxQkQsa0VBQW1CQSxDQUNqQkUsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7QUFFRyxNQUFNSSxXQUFXTCxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdlbmRhbG8vLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cz8wZjk3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcbmltcG9ydCB0eXBlIHsgRGF0YWJhc2UgfSBmcm9tICdAL3R5cGVzL2RhdGFiYXNlJztcblxuZXhwb3J0IGNvbnN0IGNyZWF0ZUNsaWVudCA9ICgpID0+XG4gIGNyZWF0ZUJyb3dzZXJDbGllbnQ8RGF0YWJhc2U+KFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhXG4gICk7XG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"29c54681db7a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdlbmRhbG8vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzAzOWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyOWM1NDY4MWRiN2FcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\app\(auth)\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _contexts_tenant_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/tenant-context */ \"(rsc)/./src/contexts/tenant-context.tsx\");\n/* harmony import */ var _components_ui_NotificationSystem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/NotificationSystem */ \"(rsc)/./src/components/ui/NotificationSystem.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Agendalo - Plataforma de Agendamiento M\\xe9dico AI-First\",\n    description: \"Agenda citas m\\xe9dicas mediante lenguaje natural con nuestra plataforma inteligente\",\n    keywords: \"agendamiento m\\xe9dico, citas m\\xe9dicas, inteligencia artificial, chatbot m\\xe9dico\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_NotificationSystem__WEBPACK_IMPORTED_MODULE_4__.NotificationProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_tenant_context__WEBPACK_IMPORTED_MODULE_3__.TenantProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/NotificationSystem.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/NotificationSystem.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ e1),
/* harmony export */   setGlobalNotificationContext: () => (/* binding */ e2),
/* harmony export */   showGlobalError: () => (/* binding */ e5),
/* harmony export */   showGlobalInfo: () => (/* binding */ e7),
/* harmony export */   showGlobalNotification: () => (/* binding */ e3),
/* harmony export */   showGlobalSuccess: () => (/* binding */ e4),
/* harmony export */   showGlobalWarning: () => (/* binding */ e6),
/* harmony export */   useNotificationHelpers: () => (/* binding */ e8),
/* harmony export */   useNotifications: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#useNotifications`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#NotificationProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#setGlobalNotificationContext`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#showGlobalNotification`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#showGlobalSuccess`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#showGlobalError`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#showGlobalWarning`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#showGlobalInfo`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\components\ui\NotificationSystem.tsx#useNotificationHelpers`);


/***/ }),

/***/ "(rsc)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\contexts\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\contexts\auth-context.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./src/contexts/tenant-context.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/tenant-context.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TenantProvider: () => (/* binding */ e0),
/* harmony export */   useTenant: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\contexts\tenant-context.tsx#TenantProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agensalud-sonnet4\src\contexts\tenant-context.tsx#useTenant`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();