"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_ai_ChatBot_tsx",{

/***/ "(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts":
/*!**********************************************!*\
  !*** ./src/lib/ai/SmartSuggestionsEngine.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartSuggestionsEngine: function() { return /* binding */ SmartSuggestionsEngine; }\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ SmartSuggestionsEngine,default auto */ /**\n * Clase principal SmartSuggestionsEngine\n */ class SmartSuggestionsEngine {\n    /**\n   * Generar sugerencias inteligentes basadas en contexto de IA\n   */ async generateSuggestions(aiContext, availableOptions, userProfile) {\n        const startTime = Date.now();\n        try {\n            // FILTRO CRÍTICO: Eliminar horarios pasados con validación de 4 horas mínimas\n            const validOptions = this.filterValidTimeSlots(availableOptions);\n            if (validOptions.length === 0) {\n                console.warn(\"⚠️ SmartSuggestionsEngine - No hay opciones v\\xe1lidas despu\\xe9s del filtrado de tiempo\");\n                return this.generateFallbackSuggestions(availableOptions);\n            }\n            // Analizar contexto y generar sugerencias base\n            const baseSuggestions = await this.analyzeAndGenerateBase(aiContext, validOptions, userProfile);\n            // Enriquecer con análisis de patrones\n            const enrichedSuggestions = await this.enrichWithPatterns(baseSuggestions, aiContext, userProfile);\n            // Aplicar filtros y ranking\n            const rankedSuggestions = this.rankAndFilter(enrichedSuggestions, aiContext);\n            // Generar insights y recomendaciones UX\n            const insights = this.generateInsights(aiContext, userProfile, rankedSuggestions);\n            const uxRecommendations = this.generateUXRecommendations(insights, rankedSuggestions);\n            const processingTime = Date.now() - startTime;\n            return {\n                suggestions: rankedSuggestions.slice(0, this.options.maxSuggestions),\n                totalAnalyzed: baseSuggestions.length,\n                processingTime,\n                confidence: this.calculateOverallConfidence(rankedSuggestions),\n                insights,\n                uxRecommendations\n            };\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            // Retornar sugerencias básicas como fallback\n            return this.generateFallbackSuggestions(availableOptions);\n        }\n    }\n    /**\n   * Analizar contexto y generar sugerencias base\n   */ async analyzeAndGenerateBase(aiContext, availableOptions, userProfile) {\n        const suggestions = [];\n        // Sugerencia basada en urgencia\n        if (aiContext.urgencyLevel === \"high\" || aiContext.urgencyLevel === \"emergency\") {\n            const urgentSuggestion = this.createUrgencySuggestion(availableOptions, aiContext);\n            if (urgentSuggestion) suggestions.push(urgentSuggestion);\n        }\n        // Sugerencia basada en preferencias de tiempo\n        if (aiContext.preferredTimeRange) {\n            const timeSuggestion = this.createTimePreferenceSuggestion(availableOptions, aiContext.preferredTimeRange);\n            if (timeSuggestion) suggestions.push(timeSuggestion);\n        }\n        // Sugerencia basada en fechas mencionadas\n        if (aiContext.suggestedDates && aiContext.suggestedDates.length > 0) {\n            const dateSuggestion = this.createDateSuggestion(availableOptions, aiContext.suggestedDates[0]);\n            if (dateSuggestion) suggestions.push(dateSuggestion);\n        }\n        // Sugerencia basada en historial de usuario\n        if (userProfile && this.options.considerHistory) {\n            const historySuggestion = this.createHistoryBasedSuggestion(availableOptions, userProfile);\n            if (historySuggestion) suggestions.push(historySuggestion);\n        }\n        // Sugerencia popular/recomendada\n        const popularSuggestion = this.createPopularSuggestion(availableOptions);\n        if (popularSuggestion) suggestions.push(popularSuggestion);\n        return suggestions;\n    }\n    /**\n   * Enriquecer sugerencias con análisis de patrones\n   */ async enrichWithPatterns(suggestions, aiContext, userProfile) {\n        return suggestions.map((suggestion)=>{\n            // Calcular métricas mejoradas\n            const enhancedMetrics = this.calculateEnhancedMetrics(suggestion, aiContext, userProfile);\n            // Generar explicación contextual mejorada\n            const enhancedExplanation = this.generateContextualExplanation(suggestion, aiContext, userProfile);\n            return {\n                ...suggestion,\n                metrics: enhancedMetrics,\n                explanation: enhancedExplanation,\n                context: {\n                    ...suggestion.context,\n                    reasoning: this.generateDetailedReasoning(suggestion, aiContext)\n                }\n            };\n        });\n    }\n    /**\n   * Ranking y filtrado de sugerencias\n   */ rankAndFilter(suggestions, aiContext) {\n        return suggestions.filter((s)=>s.confidence >= this.options.minConfidence).sort((a, b)=>{\n            // Priorizar por urgencia si es alta\n            if (aiContext.urgencyLevel === \"high\" || aiContext.urgencyLevel === \"emergency\") {\n                if (a.type === \"urgency_based\" && b.type !== \"urgency_based\") return -1;\n                if (b.type === \"urgency_based\" && a.type !== \"urgency_based\") return 1;\n            }\n            // Luego por confianza y prioridad\n            const scoreA = a.confidence * 0.6 + a.priority / 10 * 0.4;\n            const scoreB = b.confidence * 0.6 + b.priority / 10 * 0.4;\n            return scoreB - scoreA;\n        });\n    }\n    // Métodos auxiliares para crear diferentes tipos de sugerencias\n    createUrgencySuggestion(options, aiContext) {\n        // Buscar la opción más temprana disponible\n        const earliestOption = options.filter((opt)=>opt.available).sort((a, b)=>new Date(a.date + \" \" + a.time).getTime() - new Date(b.date + \" \" + b.time).getTime())[0];\n        if (!earliestOption) return null;\n        return {\n            id: \"urgent-\".concat(Date.now()),\n            type: \"urgency_based\",\n            title: \"Cita urgente disponible\",\n            description: \"\".concat(earliestOption.date, \" a las \").concat(earliestOption.time),\n            explanation: \"Recomendado por tu solicitud urgente\",\n            confidence: 0.9,\n            priority: 10,\n            data: {\n                date: earliestOption.date,\n                time: earliestOption.time,\n                doctorId: earliestOption.doctorId,\n                doctorName: earliestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.85,\n                userSatisfaction: 4.2,\n                conversionRate: 0.78,\n                popularityScore: 0.6\n            },\n            context: {\n                basedOn: [\n                    \"urgency_level\",\n                    \"earliest_available\"\n                ],\n                reasoning: \"Seleccionado por ser la opci\\xf3n m\\xe1s temprana disponible\",\n                alternatives: options.length - 1,\n                timeWindow: \"next_24_hours\"\n            },\n            actions: {\n                canBook: true,\n                canModify: false,\n                canCompare: true,\n                requiresConfirmation: true\n            }\n        };\n    }\n    createTimePreferenceSuggestion(options, timeRange) {\n        // Filtrar opciones por rango de tiempo preferido\n        const timeFiltered = options.filter((opt)=>{\n            const hour = parseInt(opt.time.split(\":\")[0]);\n            switch(timeRange){\n                case \"morning\":\n                    return hour >= 8 && hour < 12;\n                case \"afternoon\":\n                    return hour >= 12 && hour < 18;\n                case \"evening\":\n                    return hour >= 18 && hour < 21;\n                default:\n                    return true;\n            }\n        });\n        if (timeFiltered.length === 0) return null;\n        const bestOption = timeFiltered[0];\n        return {\n            id: \"time-pref-\".concat(Date.now()),\n            type: \"user_pattern\",\n            title: \"Horario de \".concat(timeRange === \"morning\" ? \"ma\\xf1ana\" : timeRange === \"afternoon\" ? \"tarde\" : \"noche\"),\n            description: \"\".concat(bestOption.date, \" a las \").concat(bestOption.time),\n            explanation: \"Coincide con tu preferencia por horarios de \".concat(timeRange === \"morning\" ? \"ma\\xf1ana\" : timeRange === \"afternoon\" ? \"tarde\" : \"noche\"),\n            confidence: 0.8,\n            priority: 8,\n            data: {\n                date: bestOption.date,\n                time: bestOption.time,\n                doctorId: bestOption.doctorId,\n                doctorName: bestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.82,\n                userSatisfaction: 4.4,\n                conversionRate: 0.75,\n                popularityScore: 0.7\n            },\n            context: {\n                basedOn: [\n                    \"time_preference\",\n                    \"conversation_analysis\"\n                ],\n                reasoning: \"Basado en tu preferencia de horario mencionada\",\n                alternatives: timeFiltered.length - 1,\n                timeWindow: timeRange\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    createDateSuggestion(options, suggestedDate) {\n        const dateOptions = options.filter((opt)=>opt.date === suggestedDate);\n        if (dateOptions.length === 0) return null;\n        const bestOption = dateOptions[0];\n        return {\n            id: \"date-\".concat(Date.now()),\n            type: \"ai_recommended\",\n            title: \"Fecha que mencionaste\",\n            description: \"\".concat(bestOption.date, \" a las \").concat(bestOption.time),\n            explanation: \"Basado en la fecha que mencionaste en nuestra conversaci\\xf3n\",\n            confidence: 0.85,\n            priority: 9,\n            data: {\n                date: bestOption.date,\n                time: bestOption.time,\n                doctorId: bestOption.doctorId,\n                doctorName: bestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.88,\n                userSatisfaction: 4.5,\n                conversionRate: 0.82,\n                popularityScore: 0.6\n            },\n            context: {\n                basedOn: [\n                    \"mentioned_date\",\n                    \"ai_analysis\"\n                ],\n                reasoning: \"Fecha espec\\xedfica mencionada en la conversaci\\xf3n\",\n                alternatives: dateOptions.length - 1,\n                timeWindow: \"specific_date\"\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    createHistoryBasedSuggestion(options, userProfile) {\n        // Analizar patrones del historial del usuario\n        const preferredTimes = userProfile.preferences.preferredTimes || [];\n        const preferredDoctors = userProfile.preferences.preferredDoctors || [];\n        const matchingOptions = options.filter((opt)=>preferredTimes.includes(opt.time) || preferredDoctors.includes(opt.doctorId));\n        if (matchingOptions.length === 0) return null;\n        const bestOption = matchingOptions[0];\n        return {\n            id: \"history-\".concat(Date.now()),\n            type: \"user_pattern\",\n            title: \"Basado en tu historial\",\n            description: \"\".concat(bestOption.date, \" a las \").concat(bestOption.time),\n            explanation: \"Coincide con tus preferencias anteriores\",\n            confidence: 0.75,\n            priority: 7,\n            data: {\n                date: bestOption.date,\n                time: bestOption.time,\n                doctorId: bestOption.doctorId,\n                doctorName: bestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.80,\n                userSatisfaction: 4.3,\n                conversionRate: 0.73,\n                popularityScore: 0.8\n            },\n            context: {\n                basedOn: [\n                    \"user_history\",\n                    \"preference_patterns\"\n                ],\n                reasoning: \"Basado en tus citas anteriores exitosas\",\n                alternatives: matchingOptions.length - 1,\n                timeWindow: \"historical_pattern\"\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    createPopularSuggestion(options) {\n        if (options.length === 0) return null;\n        // Simular popularidad basada en horarios típicos\n        const popularOption = options.find((opt)=>{\n            const hour = parseInt(opt.time.split(\":\")[0]);\n            return hour >= 9 && hour <= 11; // Horarios populares de mañana\n        }) || options[0];\n        return {\n            id: \"popular-\".concat(Date.now()),\n            type: \"popular_choice\",\n            title: \"Opci\\xf3n popular\",\n            description: \"\".concat(popularOption.date, \" a las \").concat(popularOption.time),\n            explanation: \"Horario preferido por el 80% de nuestros pacientes\",\n            confidence: 0.7,\n            priority: 6,\n            data: {\n                date: popularOption.date,\n                time: popularOption.time,\n                doctorId: popularOption.doctorId,\n                doctorName: popularOption.doctorName\n            },\n            metrics: {\n                successRate: 0.85,\n                userSatisfaction: 4.1,\n                conversionRate: 0.70,\n                popularityScore: 0.9\n            },\n            context: {\n                basedOn: [\n                    \"popularity_data\",\n                    \"user_preferences\"\n                ],\n                reasoning: \"Horario con alta satisfacci\\xf3n entre usuarios\",\n                alternatives: options.length - 1,\n                timeWindow: \"popular_hours\"\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    // Métodos auxiliares para cálculos y análisis\n    calculateEnhancedMetrics(suggestion, aiContext, userProfile) {\n        // Ajustar métricas basado en contexto\n        let adjustedSuccessRate = suggestion.metrics.successRate;\n        let adjustedSatisfaction = suggestion.metrics.userSatisfaction;\n        // Bonus por coincidencia con preferencias\n        if (aiContext.preferredTimeRange && suggestion.type === \"user_pattern\") {\n            adjustedSuccessRate += 0.05;\n            adjustedSatisfaction += 0.2;\n        }\n        // Bonus por urgencia\n        if (aiContext.urgencyLevel === \"high\" && suggestion.type === \"urgency_based\") {\n            adjustedSuccessRate += 0.1;\n        }\n        return {\n            ...suggestion.metrics,\n            successRate: Math.min(adjustedSuccessRate, 1),\n            userSatisfaction: Math.min(adjustedSatisfaction, 5)\n        };\n    }\n    generateContextualExplanation(suggestion, aiContext, userProfile) {\n        const factors = [];\n        if (aiContext.urgencyLevel === \"high\" && suggestion.type === \"urgency_based\") {\n            factors.push(\"tu solicitud urgente\");\n        }\n        if (aiContext.preferredTimeRange && suggestion.type === \"user_pattern\") {\n            factors.push(\"tu preferencia por horarios de \".concat(aiContext.preferredTimeRange));\n        }\n        if (userProfile && suggestion.type === \"user_pattern\") {\n            factors.push(\"tu historial de citas anteriores\");\n        }\n        if (suggestion.type === \"popular_choice\") {\n            factors.push(\"la alta satisfacci\\xf3n de otros pacientes\");\n        }\n        if (factors.length === 0) {\n            return suggestion.explanation;\n        }\n        return \"Recomendado basado en \".concat(factors.join(\" y \"), \".\");\n    }\n    generateDetailedReasoning(suggestion, aiContext) {\n        switch(suggestion.type){\n            case \"urgency_based\":\n                return \"Algoritmo de urgencia detect\\xf3 necesidad de atenci\\xf3n inmediata y seleccion\\xf3 la opci\\xf3n m\\xe1s temprana disponible.\";\n            case \"user_pattern\":\n                return \"An\\xe1lisis de patrones identific\\xf3 coincidencia con preferencias expresadas en la conversaci\\xf3n.\";\n            case \"ai_recommended\":\n                return \"IA proces\\xf3 el contexto de la conversaci\\xf3n y identific\\xf3 esta como la opci\\xf3n m\\xe1s alineada con tus necesidades.\";\n            case \"popular_choice\":\n                return \"An\\xe1lisis de datos hist\\xf3ricos muestra alta satisfacci\\xf3n y \\xe9xito con esta opci\\xf3n entre usuarios similares.\";\n            default:\n                return \"Recomendaci\\xf3n basada en an\\xe1lisis integral de disponibilidad y preferencias.\";\n        }\n    }\n    generateInsights(aiContext, userProfile, suggestions) {\n        var _aiContext_confidence;\n        return {\n            userProfile: userProfile ? \"returning\" : \"new\",\n            preferenceStrength: ((_aiContext_confidence = aiContext.confidence) === null || _aiContext_confidence === void 0 ? void 0 : _aiContext_confidence.overall) && aiContext.confidence.overall > 0.7 ? \"strong\" : \"moderate\",\n            urgencyLevel: aiContext.urgencyLevel || \"medium\",\n            flexibilityScore: aiContext.flexibilityLevel === \"very-flexible\" ? 0.9 : aiContext.flexibilityLevel === \"flexible\" ? 0.6 : 0.3,\n            predictedSatisfaction: suggestions.length > 0 ? suggestions.reduce((acc, s)=>acc + s.metrics.userSatisfaction, 0) / suggestions.length / 5 : 0.5\n        };\n    }\n    generateUXRecommendations(insights, suggestions) {\n        return {\n            showComparison: suggestions.length > 2,\n            highlightBestOption: suggestions.length > 0 && suggestions[0].confidence > 0.8,\n            showExplanations: insights.preferenceStrength === \"strong\",\n            enableQuickBook: insights.urgencyLevel === \"high\",\n            suggestAlternatives: insights.flexibilityScore > 0.6\n        };\n    }\n    calculateOverallConfidence(suggestions) {\n        if (suggestions.length === 0) return 0;\n        return suggestions.reduce((acc, s)=>acc + s.confidence, 0) / suggestions.length;\n    }\n    generateFallbackSuggestions(options) {\n        return {\n            suggestions: [],\n            totalAnalyzed: 0,\n            processingTime: 0,\n            confidence: 0.3,\n            insights: {\n                userProfile: \"new\",\n                preferenceStrength: \"weak\",\n                urgencyLevel: \"medium\",\n                flexibilityScore: 0.5,\n                predictedSatisfaction: 0.5\n            },\n            uxRecommendations: {\n                showComparison: false,\n                highlightBestOption: false,\n                showExplanations: false,\n                enableQuickBook: false,\n                suggestAlternatives: true\n            }\n        };\n    }\n    /**\n   * Filtra horarios válidos eliminando fechas pasadas y aplicando regla de 4 horas mínimas\n   *\n   * REGLA CRÍTICA: Para el día actual, solo permitir horarios con mínimo 4 horas de anticipación\n   * Para días futuros, permitir todos los horarios\n   *\n   * @param options - Opciones de horarios disponibles\n   * @returns Opciones filtradas que cumplen con las reglas de tiempo\n   */ filterValidTimeSlots(options) {\n        const now = new Date();\n        const currentDate = now.toISOString().split(\"T\")[0]; // YYYY-MM-DD\n        const currentTime = now.getHours() * 60 + now.getMinutes(); // Minutos desde medianoche\n        const MINIMUM_ADVANCE_HOURS = 4;\n        const MINIMUM_ADVANCE_MINUTES = MINIMUM_ADVANCE_HOURS * 60;\n        return options.filter((option)=>{\n            try {\n                const optionDate = option.date;\n                const optionTime = option.time;\n                if (!optionDate || !optionTime) {\n                    console.warn(\"⚠️ SmartSuggestionsEngine - Opci\\xf3n sin fecha o hora:\", option);\n                    return false;\n                }\n                // Comparar fechas\n                if (optionDate < currentDate) {\n                    // Fecha pasada - rechazar\n                    return false;\n                } else if (optionDate > currentDate) {\n                    // Fecha futura - aceptar todos los horarios\n                    return true;\n                } else {\n                    // Fecha actual - aplicar regla de 4 horas mínimas\n                    const [hours, minutes] = optionTime.split(\":\").map(Number);\n                    const optionTimeInMinutes = hours * 60 + minutes;\n                    const timeDifferenceMinutes = optionTimeInMinutes - currentTime;\n                    if (timeDifferenceMinutes < MINIMUM_ADVANCE_MINUTES) {\n                        console.log(\"\\uD83D\\uDEAB SmartSuggestionsEngine - Horario rechazado por regla de 4h: \".concat(optionTime, \" (diferencia: \").concat(timeDifferenceMinutes, \" min)\"));\n                        return false;\n                    }\n                    console.log(\"✅ SmartSuggestionsEngine - Horario aceptado: \".concat(optionTime, \" (diferencia: \").concat(timeDifferenceMinutes, \" min)\"));\n                    return true;\n                }\n            } catch (error) {\n                console.error(\"❌ SmartSuggestionsEngine - Error procesando opci\\xf3n:\", option, error);\n                return false;\n            }\n        });\n    }\n    constructor(organizationId, options = {}){\n        this.userProfiles = new Map();\n        this.organizationId = organizationId;\n        this.options = {\n            maxSuggestions: 5,\n            minConfidence: 0.6,\n            includeExplanations: true,\n            personalizeForUser: true,\n            considerHistory: true,\n            optimizeForConversion: true,\n            ...options\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (SmartSuggestionsEngine);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\n"));

/***/ })

});