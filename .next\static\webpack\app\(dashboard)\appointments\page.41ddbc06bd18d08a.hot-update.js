"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/page",{

/***/ "(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx":
/*!***************************************************************!*\
  !*** ./src/components/appointments/AvailabilityIndicator.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeeklyAvailability: function() { return /* binding */ WeeklyAvailability; },\n/* harmony export */   useWeeklyAvailabilityData: function() { return /* binding */ useWeeklyAvailabilityData; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* __next_internal_client_entry_do_not_use__ WeeklyAvailability,useWeeklyAvailabilityData,default auto */ \n/**\n * AvailabilityIndicator Component\n * \n * Componente para mostrar indicadores visuales de disponibilidad\n * con colores semafóricos y información contextual\n * \n * Características:\n * - Indicadores de densidad (Alta/Media/Baja/No disponible)\n * - Tooltips informativos con detalles\n * - Animaciones suaves para feedback visual\n * - Accesibilidad WCAG 2.1 completa\n * \n * <AUTHOR> MVP Team - UX Enhancement\n * @version 1.0.0\n */ \n\n/**\n * Determina el nivel de disponibilidad basado en el número de slots\n */ const getAvailabilityLevel = (slotsCount)=>{\n    if (slotsCount === 0) return \"none\";\n    if (slotsCount <= 2) return \"low\";\n    if (slotsCount <= 5) return \"medium\";\n    return \"high\";\n};\n/**\n * Configuración de estilos por nivel de disponibilidad\n */ const availabilityConfig = {\n    high: {\n        color: \"bg-green-500\",\n        lightColor: \"bg-green-100\",\n        textColor: \"text-green-700\",\n        borderColor: \"border-green-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        label: \"Alta disponibilidad\",\n        description: \"Muchos horarios disponibles\"\n    },\n    medium: {\n        color: \"bg-yellow-500\",\n        lightColor: \"bg-yellow-100\",\n        textColor: \"text-yellow-700\",\n        borderColor: \"border-yellow-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        label: \"Disponibilidad media\",\n        description: \"Algunos horarios disponibles\"\n    },\n    low: {\n        color: \"bg-red-500\",\n        lightColor: \"bg-red-100\",\n        textColor: \"text-red-700\",\n        borderColor: \"border-red-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        label: \"Baja disponibilidad\",\n        description: \"Pocos horarios disponibles\"\n    },\n    none: {\n        color: \"bg-gray-400\",\n        lightColor: \"bg-gray-100\",\n        textColor: \"text-gray-500\",\n        borderColor: \"border-gray-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"No disponible\",\n        description: \"Sin horarios disponibles\"\n    },\n    blocked: {\n        color: \"bg-gray-500\",\n        lightColor: \"bg-gray-50\",\n        textColor: \"text-gray-400\",\n        borderColor: \"border-gray-200\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"Bloqueado\",\n        description: \"No disponible por reglas de reserva\"\n    }\n};\n/**\n * Configuración de tamaños\n */ const sizeConfig = {\n    sm: {\n        container: \"w-16 h-16\",\n        indicator: \"w-3 h-3\",\n        text: \"text-xs\",\n        padding: \"p-2\"\n    },\n    md: {\n        container: \"w-20 h-20\",\n        indicator: \"w-4 h-4\",\n        text: \"text-sm\",\n        padding: \"p-3\"\n    },\n    lg: {\n        container: \"w-24 h-24\",\n        indicator: \"w-5 h-5\",\n        text: \"text-base\",\n        padding: \"p-4\"\n    }\n};\n/**\n * Componente principal AvailabilityIndicator\n */ const AvailabilityIndicator = (param)=>{\n    let { slotsCount, date, dayName, isSelected = false, onClick, size = \"md\", compact = false, isBlocked = false, blockReason } = param;\n    // CRITICAL FEATURE: Use blocked state if date is blocked, otherwise use availability level\n    const level = isBlocked ? \"blocked\" : getAvailabilityLevel(slotsCount);\n    const config = availabilityConfig[level];\n    const sizeStyles = sizeConfig[size];\n    const IconComponent = config.icon;\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.getDate().toString();\n    };\n    const formatFullDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"es-ES\", {\n            weekday: \"long\",\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const handleClick = ()=>{\n        // CRITICAL FEATURE: Block click if date is blocked\n        if (isBlocked) {\n            console.log(\"\\uD83D\\uDEAB CLICK BLOQUEADO - Fecha no disponible:\", blockReason);\n            return;\n        }\n        // Validar que no sea fecha pasada\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const dateObj = new Date(date);\n        dateObj.setHours(0, 0, 0, 0);\n        const isPastDate = dateObj.getTime() < today.getTime();\n        if (onClick && level !== \"none\" && level !== \"blocked\" && !isPastDate) {\n            onClick();\n        }\n    };\n    // Verificar si es fecha pasada\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const dateObj = new Date(date);\n    dateObj.setHours(0, 0, 0, 0);\n    const isPastDate = dateObj.getTime() < today.getTime();\n    // CRITICAL FEATURE: Include blocked state in clickable logic\n    const isClickable = onClick && level !== \"none\" && level !== \"blocked\" && !isPastDate && !isBlocked;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          \".concat(sizeStyles.container, \" \").concat(sizeStyles.padding, \"\\n          \").concat(config.lightColor, \" \").concat(config.borderColor, \"\\n          border-2 rounded-lg\\n          flex flex-col items-center justify-center\\n          transition-all duration-200 ease-in-out\\n          \").concat(isClickable ? \"cursor-pointer hover:shadow-md hover:scale-105\" : \"cursor-default\", \"\\n          \").concat(isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"\", \"\\n          \").concat(level === \"none\" || isPastDate ? \"opacity-60\" : \"\", \"\\n          \").concat(isPastDate ? \"grayscale\" : \"\", \"\\n        \"),\n                onClick: handleClick,\n                role: isClickable ? \"button\" : \"presentation\",\n                tabIndex: isClickable ? 0 : -1,\n                \"aria-label\": isBlocked ? \"\".concat(formatFullDate(date), \", \").concat(config.label, \", \").concat(blockReason || \"No disponible por reglas de reserva\") : \"\".concat(formatFullDate(date), \", \").concat(config.label, \", \").concat(slotsCount, \" horarios disponibles\"),\n                onKeyDown: (e)=>{\n                    if (isClickable && (e.key === \"Enter\" || e.key === \" \")) {\n                        e.preventDefault();\n                        handleClick();\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(config.color, \" \").concat(sizeStyles.indicator, \" rounded-full flex items-center justify-center\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"w-2 h-2 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(sizeStyles.text, \" font-semibold text-gray-900 text-center\"),\n                        children: formatDate(date)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    dayName && !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(sizeStyles.text, \" text-gray-600 text-center leading-tight\"),\n                        children: dayName.substring(0, 3)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined),\n                    !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(sizeStyles.text, \" \").concat(config.textColor, \" text-center font-medium\"),\n                        children: [\n                            slotsCount,\n                            \" slot\",\n                            slotsCount !== 1 ? \"s\" : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: formatFullDate(date)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-300\",\n                        children: config.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    isBlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-300 font-medium\",\n                        children: blockReason || \"No disponible por reglas de reserva\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-300\",\n                        children: [\n                            slotsCount,\n                            \" horario\",\n                            slotsCount !== 1 ? \"s\" : \"\",\n                            \" disponible\",\n                            slotsCount !== 1 ? \"s\" : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AvailabilityIndicator;\nconst WeeklyAvailability = (param)=>{\n    let { weekData, selectedDate, onDateSelect, size = \"md\" } = param;\n    /**\n   * CRITICAL FIX: Ensure timezone-safe date passing\n   * The day.date should already be timezone-safe from WeeklyAvailabilitySelector,\n   * but we add validation to ensure consistency\n   */ const handleDateClick = (dateString)=>{\n        // DEBUG: Log para verificar fecha antes de enviar\n        console.log(\"=== DEBUG WEEKLY AVAILABILITY CLICK ===\");\n        console.log(\"day.date recibido:\", dateString);\n        // Verificar que la fecha esté en formato correcto YYYY-MM-DD\n        const dateRegex = /^\\d{4}-\\d{2}-\\d{2}$/;\n        if (!dateRegex.test(dateString)) {\n            console.error(\"FORMATO DE FECHA INCORRECTO:\", dateString);\n            return;\n        }\n        // CRITICAL FIX: Verificar consistencia timezone usando parsing seguro\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = dateString.split(\"-\").map(Number);\n        const dateObjSafe = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObjSafe.getFullYear(), \"-\").concat(String(dateObjSafe.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObjSafe.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(dateString); // This creates the problematic UTC interpretation\n        const utcLocalString = \"\".concat(dateObjUTC.getFullYear(), \"-\").concat(String(dateObjUTC.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObjUTC.getDate()).padStart(2, \"0\"));\n        console.log(\"Verificaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - dateString original:\", dateString);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"  - utcLocalString (problematic):\", utcLocalString);\n        console.log(\"  - \\xbfSon iguales? (safe):\", dateString === localDateString);\n        console.log(\"  - \\xbfSon iguales? (UTC):\", dateString === utcLocalString);\n        // CRITICAL FIX: Use timezone-safe comparison for decision\n        if (dateString !== localDateString) {\n            console.warn(\"DESFASE TIMEZONE DETECTADO - usando fecha local corregida\");\n            console.log(\"Enviando fecha corregida:\", localDateString);\n            onDateSelect === null || onDateSelect === void 0 ? void 0 : onDateSelect(localDateString);\n        } else {\n            console.log(\"Fecha consistente (timezone-safe) - enviando original:\", dateString);\n            onDateSelect === null || onDateSelect === void 0 ? void 0 : onDateSelect(dateString);\n        }\n        console.log(\"=========================================\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center space-x-2\",\n        children: weekData.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvailabilityIndicator, {\n                date: day.date,\n                dayName: day.dayName,\n                slotsCount: day.slotsCount,\n                isSelected: selectedDate === day.date,\n                onClick: ()=>handleDateClick(day.date),\n                size: size,\n                isBlocked: day.isBlocked,\n                blockReason: day.blockReason\n            }, day.date, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n        lineNumber: 355,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = WeeklyAvailability;\n/**\n * Hook para generar datos de ejemplo de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate)=>{\n    const weekData = [];\n    const dayNames = [\n        \"Domingo\",\n        \"Lunes\",\n        \"Martes\",\n        \"Mi\\xe9rcoles\",\n        \"Jueves\",\n        \"Viernes\",\n        \"S\\xe1bado\"\n    ];\n    for(let i = 0; i < 7; i++){\n        const date = new Date(startDate);\n        date.setDate(startDate.getDate() + i);\n        // Simular disponibilidad variable\n        const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n        const slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n        weekData.push({\n            date: date.toISOString().split(\"T\")[0],\n            dayName: dayNames[date.getDay()],\n            slotsCount\n        });\n    }\n    return weekData;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (AvailabilityIndicator);\nvar _c, _c1;\n$RefreshReg$(_c, \"AvailabilityIndicator\");\n$RefreshReg$(_c1, \"WeeklyAvailability\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\n"));

/***/ })

});