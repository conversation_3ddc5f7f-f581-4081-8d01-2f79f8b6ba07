"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_ai_ChatBot_tsx",{

/***/ "(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts":
/*!**********************************************!*\
  !*** ./src/lib/ai/SmartSuggestionsEngine.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartSuggestionsEngine: function() { return /* binding */ SmartSuggestionsEngine; }\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ SmartSuggestionsEngine,default auto */ /**\n * Clase principal SmartSuggestionsEngine\n */ class SmartSuggestionsEngine {\n    /**\n   * Generar sugerencias inteligentes basadas en contexto de IA\n   */ async generateSuggestions(aiContext, availableOptions, userProfile) {\n        const startTime = Date.now();\n        try {\n            // FILTRO CRÍTICO: Eliminar horarios pasados con validación de 4 horas mínimas\n            const validOptions = this.filterValidTimeSlots(availableOptions);\n            if (validOptions.length === 0) {\n                console.warn(\"⚠️ SmartSuggestionsEngine - No hay opciones v\\xe1lidas despu\\xe9s del filtrado de tiempo\");\n                return this.generateFallbackSuggestions(availableOptions);\n            }\n            // Analizar contexto y generar sugerencias base\n            const baseSuggestions = await this.analyzeAndGenerateBase(aiContext, validOptions, userProfile);\n            // Enriquecer con análisis de patrones\n            const enrichedSuggestions = await this.enrichWithPatterns(baseSuggestions, aiContext, userProfile);\n            // Aplicar filtros y ranking\n            const rankedSuggestions = this.rankAndFilter(enrichedSuggestions, aiContext);\n            // Generar insights y recomendaciones UX\n            const insights = this.generateInsights(aiContext, userProfile, rankedSuggestions);\n            const uxRecommendations = this.generateUXRecommendations(insights, rankedSuggestions);\n            const processingTime = Date.now() - startTime;\n            return {\n                suggestions: rankedSuggestions.slice(0, this.options.maxSuggestions),\n                totalAnalyzed: baseSuggestions.length,\n                processingTime,\n                confidence: this.calculateOverallConfidence(rankedSuggestions),\n                insights,\n                uxRecommendations\n            };\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            // Retornar sugerencias básicas como fallback\n            return this.generateFallbackSuggestions(availableOptions);\n        }\n    }\n    /**\n   * Analizar contexto y generar sugerencias base\n   */ async analyzeAndGenerateBase(aiContext, availableOptions, userProfile) {\n        const suggestions = [];\n        // Sugerencia basada en urgencia\n        if (aiContext.urgencyLevel === \"high\" || aiContext.urgencyLevel === \"emergency\") {\n            const urgentSuggestion = this.createUrgencySuggestion(availableOptions, aiContext);\n            if (urgentSuggestion) suggestions.push(urgentSuggestion);\n        }\n        // Sugerencia basada en preferencias de tiempo\n        if (aiContext.preferredTimeRange) {\n            const timeSuggestion = this.createTimePreferenceSuggestion(availableOptions, aiContext.preferredTimeRange);\n            if (timeSuggestion) suggestions.push(timeSuggestion);\n        }\n        // Sugerencia basada en fechas mencionadas\n        if (aiContext.suggestedDates && aiContext.suggestedDates.length > 0) {\n            const dateSuggestion = this.createDateSuggestion(availableOptions, aiContext.suggestedDates[0]);\n            if (dateSuggestion) suggestions.push(dateSuggestion);\n        }\n        // Sugerencia basada en historial de usuario\n        if (userProfile && this.options.considerHistory) {\n            const historySuggestion = this.createHistoryBasedSuggestion(availableOptions, userProfile);\n            if (historySuggestion) suggestions.push(historySuggestion);\n        }\n        // Sugerencia popular/recomendada\n        const popularSuggestion = this.createPopularSuggestion(availableOptions);\n        if (popularSuggestion) suggestions.push(popularSuggestion);\n        return suggestions;\n    }\n    /**\n   * Enriquecer sugerencias con análisis de patrones\n   */ async enrichWithPatterns(suggestions, aiContext, userProfile) {\n        return suggestions.map((suggestion)=>{\n            // Calcular métricas mejoradas\n            const enhancedMetrics = this.calculateEnhancedMetrics(suggestion, aiContext, userProfile);\n            // Generar explicación contextual mejorada\n            const enhancedExplanation = this.generateContextualExplanation(suggestion, aiContext, userProfile);\n            return {\n                ...suggestion,\n                metrics: enhancedMetrics,\n                explanation: enhancedExplanation,\n                context: {\n                    ...suggestion.context,\n                    reasoning: this.generateDetailedReasoning(suggestion, aiContext)\n                }\n            };\n        });\n    }\n    /**\n   * Ranking y filtrado de sugerencias\n   */ rankAndFilter(suggestions, aiContext) {\n        return suggestions.filter((s)=>s.confidence >= this.options.minConfidence).sort((a, b)=>{\n            // Priorizar por urgencia si es alta\n            if (aiContext.urgencyLevel === \"high\" || aiContext.urgencyLevel === \"emergency\") {\n                if (a.type === \"urgency_based\" && b.type !== \"urgency_based\") return -1;\n                if (b.type === \"urgency_based\" && a.type !== \"urgency_based\") return 1;\n            }\n            // Luego por confianza y prioridad\n            const scoreA = a.confidence * 0.6 + a.priority / 10 * 0.4;\n            const scoreB = b.confidence * 0.6 + b.priority / 10 * 0.4;\n            return scoreB - scoreA;\n        });\n    }\n    // Métodos auxiliares para crear diferentes tipos de sugerencias\n    createUrgencySuggestion(options, aiContext) {\n        // Buscar la opción más temprana disponible\n        const earliestOption = options.filter((opt)=>opt.available).sort((a, b)=>new Date(a.date + \" \" + a.time).getTime() - new Date(b.date + \" \" + b.time).getTime())[0];\n        if (!earliestOption) return null;\n        return {\n            id: \"urgent-\".concat(Date.now()),\n            type: \"urgency_based\",\n            title: \"Cita urgente disponible\",\n            description: \"\".concat(earliestOption.date, \" a las \").concat(earliestOption.time),\n            explanation: \"Recomendado por tu solicitud urgente\",\n            confidence: 0.9,\n            priority: 10,\n            data: {\n                date: earliestOption.date,\n                time: earliestOption.time,\n                doctorId: earliestOption.doctorId,\n                doctorName: earliestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.85,\n                userSatisfaction: 4.2,\n                conversionRate: 0.78,\n                popularityScore: 0.6\n            },\n            context: {\n                basedOn: [\n                    \"urgency_level\",\n                    \"earliest_available\"\n                ],\n                reasoning: \"Seleccionado por ser la opci\\xf3n m\\xe1s temprana disponible\",\n                alternatives: options.length - 1,\n                timeWindow: \"next_24_hours\"\n            },\n            actions: {\n                canBook: true,\n                canModify: false,\n                canCompare: true,\n                requiresConfirmation: true\n            }\n        };\n    }\n    createTimePreferenceSuggestion(options, timeRange) {\n        // Filtrar opciones por rango de tiempo preferido\n        const timeFiltered = options.filter((opt)=>{\n            const hour = parseInt(opt.time.split(\":\")[0]);\n            switch(timeRange){\n                case \"morning\":\n                    return hour >= 8 && hour < 12;\n                case \"afternoon\":\n                    return hour >= 12 && hour < 18;\n                case \"evening\":\n                    return hour >= 18 && hour < 21;\n                default:\n                    return true;\n            }\n        });\n        if (timeFiltered.length === 0) return null;\n        const bestOption = timeFiltered[0];\n        return {\n            id: \"time-pref-\".concat(Date.now()),\n            type: \"user_pattern\",\n            title: \"Horario de \".concat(timeRange === \"morning\" ? \"ma\\xf1ana\" : timeRange === \"afternoon\" ? \"tarde\" : \"noche\"),\n            description: \"\".concat(bestOption.date, \" a las \").concat(bestOption.time),\n            explanation: \"Coincide con tu preferencia por horarios de \".concat(timeRange === \"morning\" ? \"ma\\xf1ana\" : timeRange === \"afternoon\" ? \"tarde\" : \"noche\"),\n            confidence: 0.8,\n            priority: 8,\n            data: {\n                date: bestOption.date,\n                time: bestOption.time,\n                doctorId: bestOption.doctorId,\n                doctorName: bestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.82,\n                userSatisfaction: 4.4,\n                conversionRate: 0.75,\n                popularityScore: 0.7\n            },\n            context: {\n                basedOn: [\n                    \"time_preference\",\n                    \"conversation_analysis\"\n                ],\n                reasoning: \"Basado en tu preferencia de horario mencionada\",\n                alternatives: timeFiltered.length - 1,\n                timeWindow: timeRange\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    createDateSuggestion(options, suggestedDate) {\n        const dateOptions = options.filter((opt)=>opt.date === suggestedDate);\n        if (dateOptions.length === 0) return null;\n        const bestOption = dateOptions[0];\n        return {\n            id: \"date-\".concat(Date.now()),\n            type: \"ai_recommended\",\n            title: \"Fecha que mencionaste\",\n            description: \"\".concat(bestOption.date, \" a las \").concat(bestOption.time),\n            explanation: \"Basado en la fecha que mencionaste en nuestra conversaci\\xf3n\",\n            confidence: 0.85,\n            priority: 9,\n            data: {\n                date: bestOption.date,\n                time: bestOption.time,\n                doctorId: bestOption.doctorId,\n                doctorName: bestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.88,\n                userSatisfaction: 4.5,\n                conversionRate: 0.82,\n                popularityScore: 0.6\n            },\n            context: {\n                basedOn: [\n                    \"mentioned_date\",\n                    \"ai_analysis\"\n                ],\n                reasoning: \"Fecha espec\\xedfica mencionada en la conversaci\\xf3n\",\n                alternatives: dateOptions.length - 1,\n                timeWindow: \"specific_date\"\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    createHistoryBasedSuggestion(options, userProfile) {\n        // Analizar patrones del historial del usuario\n        const preferredTimes = userProfile.preferences.preferredTimes || [];\n        const preferredDoctors = userProfile.preferences.preferredDoctors || [];\n        const matchingOptions = options.filter((opt)=>preferredTimes.includes(opt.time) || preferredDoctors.includes(opt.doctorId));\n        if (matchingOptions.length === 0) return null;\n        const bestOption = matchingOptions[0];\n        return {\n            id: \"history-\".concat(Date.now()),\n            type: \"user_pattern\",\n            title: \"Basado en tu historial\",\n            description: \"\".concat(bestOption.date, \" a las \").concat(bestOption.time),\n            explanation: \"Coincide con tus preferencias anteriores\",\n            confidence: 0.75,\n            priority: 7,\n            data: {\n                date: bestOption.date,\n                time: bestOption.time,\n                doctorId: bestOption.doctorId,\n                doctorName: bestOption.doctorName\n            },\n            metrics: {\n                successRate: 0.80,\n                userSatisfaction: 4.3,\n                conversionRate: 0.73,\n                popularityScore: 0.8\n            },\n            context: {\n                basedOn: [\n                    \"user_history\",\n                    \"preference_patterns\"\n                ],\n                reasoning: \"Basado en tus citas anteriores exitosas\",\n                alternatives: matchingOptions.length - 1,\n                timeWindow: \"historical_pattern\"\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    createPopularSuggestion(options) {\n        if (options.length === 0) return null;\n        // Simular popularidad basada en horarios típicos\n        const popularOption = options.find((opt)=>{\n            const hour = parseInt(opt.time.split(\":\")[0]);\n            return hour >= 9 && hour <= 11; // Horarios populares de mañana\n        }) || options[0];\n        return {\n            id: \"popular-\".concat(Date.now()),\n            type: \"popular_choice\",\n            title: \"Opci\\xf3n popular\",\n            description: \"\".concat(popularOption.date, \" a las \").concat(popularOption.time),\n            explanation: \"Horario preferido por el 80% de nuestros pacientes\",\n            confidence: 0.7,\n            priority: 6,\n            data: {\n                date: popularOption.date,\n                time: popularOption.time,\n                doctorId: popularOption.doctorId,\n                doctorName: popularOption.doctorName\n            },\n            metrics: {\n                successRate: 0.85,\n                userSatisfaction: 4.1,\n                conversionRate: 0.70,\n                popularityScore: 0.9\n            },\n            context: {\n                basedOn: [\n                    \"popularity_data\",\n                    \"user_preferences\"\n                ],\n                reasoning: \"Horario con alta satisfacci\\xf3n entre usuarios\",\n                alternatives: options.length - 1,\n                timeWindow: \"popular_hours\"\n            },\n            actions: {\n                canBook: true,\n                canModify: true,\n                canCompare: true,\n                requiresConfirmation: false\n            }\n        };\n    }\n    // Métodos auxiliares para cálculos y análisis\n    calculateEnhancedMetrics(suggestion, aiContext, userProfile) {\n        // Ajustar métricas basado en contexto\n        let adjustedSuccessRate = suggestion.metrics.successRate;\n        let adjustedSatisfaction = suggestion.metrics.userSatisfaction;\n        // Bonus por coincidencia con preferencias\n        if (aiContext.preferredTimeRange && suggestion.type === \"user_pattern\") {\n            adjustedSuccessRate += 0.05;\n            adjustedSatisfaction += 0.2;\n        }\n        // Bonus por urgencia\n        if (aiContext.urgencyLevel === \"high\" && suggestion.type === \"urgency_based\") {\n            adjustedSuccessRate += 0.1;\n        }\n        return {\n            ...suggestion.metrics,\n            successRate: Math.min(adjustedSuccessRate, 1),\n            userSatisfaction: Math.min(adjustedSatisfaction, 5)\n        };\n    }\n    generateContextualExplanation(suggestion, aiContext, userProfile) {\n        const factors = [];\n        if (aiContext.urgencyLevel === \"high\" && suggestion.type === \"urgency_based\") {\n            factors.push(\"tu solicitud urgente\");\n        }\n        if (aiContext.preferredTimeRange && suggestion.type === \"user_pattern\") {\n            factors.push(\"tu preferencia por horarios de \".concat(aiContext.preferredTimeRange));\n        }\n        if (userProfile && suggestion.type === \"user_pattern\") {\n            factors.push(\"tu historial de citas anteriores\");\n        }\n        if (suggestion.type === \"popular_choice\") {\n            factors.push(\"la alta satisfacci\\xf3n de otros pacientes\");\n        }\n        if (factors.length === 0) {\n            return suggestion.explanation;\n        }\n        return \"Recomendado basado en \".concat(factors.join(\" y \"), \".\");\n    }\n    generateDetailedReasoning(suggestion, aiContext) {\n        switch(suggestion.type){\n            case \"urgency_based\":\n                return \"Algoritmo de urgencia detect\\xf3 necesidad de atenci\\xf3n inmediata y seleccion\\xf3 la opci\\xf3n m\\xe1s temprana disponible.\";\n            case \"user_pattern\":\n                return \"An\\xe1lisis de patrones identific\\xf3 coincidencia con preferencias expresadas en la conversaci\\xf3n.\";\n            case \"ai_recommended\":\n                return \"IA proces\\xf3 el contexto de la conversaci\\xf3n y identific\\xf3 esta como la opci\\xf3n m\\xe1s alineada con tus necesidades.\";\n            case \"popular_choice\":\n                return \"An\\xe1lisis de datos hist\\xf3ricos muestra alta satisfacci\\xf3n y \\xe9xito con esta opci\\xf3n entre usuarios similares.\";\n            default:\n                return \"Recomendaci\\xf3n basada en an\\xe1lisis integral de disponibilidad y preferencias.\";\n        }\n    }\n    generateInsights(aiContext, userProfile, suggestions) {\n        var _aiContext_confidence;\n        return {\n            userProfile: userProfile ? \"returning\" : \"new\",\n            preferenceStrength: ((_aiContext_confidence = aiContext.confidence) === null || _aiContext_confidence === void 0 ? void 0 : _aiContext_confidence.overall) && aiContext.confidence.overall > 0.7 ? \"strong\" : \"moderate\",\n            urgencyLevel: aiContext.urgencyLevel || \"medium\",\n            flexibilityScore: aiContext.flexibilityLevel === \"very-flexible\" ? 0.9 : aiContext.flexibilityLevel === \"flexible\" ? 0.6 : 0.3,\n            predictedSatisfaction: suggestions.length > 0 ? suggestions.reduce((acc, s)=>acc + s.metrics.userSatisfaction, 0) / suggestions.length / 5 : 0.5\n        };\n    }\n    generateUXRecommendations(insights, suggestions) {\n        return {\n            showComparison: suggestions.length > 2,\n            highlightBestOption: suggestions.length > 0 && suggestions[0].confidence > 0.8,\n            showExplanations: insights.preferenceStrength === \"strong\",\n            enableQuickBook: insights.urgencyLevel === \"high\",\n            suggestAlternatives: insights.flexibilityScore > 0.6\n        };\n    }\n    calculateOverallConfidence(suggestions) {\n        if (suggestions.length === 0) return 0;\n        return suggestions.reduce((acc, s)=>acc + s.confidence, 0) / suggestions.length;\n    }\n    generateFallbackSuggestions(options) {\n        return {\n            suggestions: [],\n            totalAnalyzed: 0,\n            processingTime: 0,\n            confidence: 0.3,\n            insights: {\n                userProfile: \"new\",\n                preferenceStrength: \"weak\",\n                urgencyLevel: \"medium\",\n                flexibilityScore: 0.5,\n                predictedSatisfaction: 0.5\n            },\n            uxRecommendations: {\n                showComparison: false,\n                highlightBestOption: false,\n                showExplanations: false,\n                enableQuickBook: false,\n                suggestAlternatives: true\n            }\n        };\n    }\n    constructor(organizationId, options = {}){\n        this.userProfiles = new Map();\n        this.organizationId = organizationId;\n        this.options = {\n            maxSuggestions: 5,\n            minConfidence: 0.6,\n            includeExplanations: true,\n            personalizeForUser: true,\n            considerHistory: true,\n            optimizeForConversion: true,\n            ...options\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (SmartSuggestionsEngine);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\n"));

/***/ })

});