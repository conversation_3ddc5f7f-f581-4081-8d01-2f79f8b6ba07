"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic using timezone-safe objects\n        const utcDateStringFromUTC = dateObjUTC.toISOString().split(\"T\")[0];\n        const utcDateStringFromLocal = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateStringFromUTC;\n        console.log(\"Comparaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString (from UTC obj):\", utcDateStringFromUTC);\n        console.log(\"  - utcDateString (from local obj):\", utcDateStringFromLocal);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"\\xbfDate objects son consistentes?:\", localDateString === date);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 526,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 539,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 569,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 606,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 614,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 633,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 632,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 648,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 673,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 514,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"4O/41eNoaHqwK0PAWV/sHVC2lvE=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});