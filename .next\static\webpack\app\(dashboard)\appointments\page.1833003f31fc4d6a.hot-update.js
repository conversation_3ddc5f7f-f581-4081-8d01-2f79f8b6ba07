"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/page",{

/***/ "(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx":
/*!***************************************************************!*\
  !*** ./src/components/appointments/AvailabilityIndicator.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeeklyAvailability: function() { return /* binding */ WeeklyAvailability; },\n/* harmony export */   useWeeklyAvailabilityData: function() { return /* binding */ useWeeklyAvailabilityData; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* __next_internal_client_entry_do_not_use__ WeeklyAvailability,useWeeklyAvailabilityData,default auto */ \n/**\n * AvailabilityIndicator Component\n * \n * Componente para mostrar indicadores visuales de disponibilidad\n * con colores semafóricos y información contextual\n * \n * Características:\n * - Indicadores de densidad (Alta/Media/Baja/No disponible)\n * - Tooltips informativos con detalles\n * - Animaciones suaves para feedback visual\n * - Accesibilidad WCAG 2.1 completa\n * \n * <AUTHOR> MVP Team - UX Enhancement\n * @version 1.0.0\n */ \n\n/**\n * Determina el nivel de disponibilidad basado en el número de slots\n */ const getAvailabilityLevel = (slotsCount)=>{\n    if (slotsCount === 0) return \"none\";\n    if (slotsCount <= 2) return \"low\";\n    if (slotsCount <= 5) return \"medium\";\n    return \"high\";\n};\n/**\n * Configuración de estilos por nivel de disponibilidad\n */ const availabilityConfig = {\n    high: {\n        color: \"bg-green-500\",\n        lightColor: \"bg-green-100\",\n        textColor: \"text-green-700\",\n        borderColor: \"border-green-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        label: \"Alta disponibilidad\",\n        description: \"Muchos horarios disponibles\"\n    },\n    medium: {\n        color: \"bg-yellow-500\",\n        lightColor: \"bg-yellow-100\",\n        textColor: \"text-yellow-700\",\n        borderColor: \"border-yellow-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        label: \"Disponibilidad media\",\n        description: \"Algunos horarios disponibles\"\n    },\n    low: {\n        color: \"bg-red-500\",\n        lightColor: \"bg-red-100\",\n        textColor: \"text-red-700\",\n        borderColor: \"border-red-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        label: \"Baja disponibilidad\",\n        description: \"Pocos horarios disponibles\"\n    },\n    none: {\n        color: \"bg-gray-400\",\n        lightColor: \"bg-gray-100\",\n        textColor: \"text-gray-500\",\n        borderColor: \"border-gray-300\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"No disponible\",\n        description: \"Sin horarios disponibles\"\n    },\n    blocked: {\n        color: \"bg-gray-500\",\n        lightColor: \"bg-gray-50\",\n        textColor: \"text-gray-400\",\n        borderColor: \"border-gray-200\",\n        icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"Bloqueado\",\n        description: \"No disponible por reglas de reserva\"\n    }\n};\n/**\n * Configuración de tamaños\n */ const sizeConfig = {\n    sm: {\n        container: \"w-16 h-16\",\n        indicator: \"w-3 h-3\",\n        text: \"text-xs\",\n        padding: \"p-2\"\n    },\n    md: {\n        container: \"w-20 h-20\",\n        indicator: \"w-4 h-4\",\n        text: \"text-sm\",\n        padding: \"p-3\"\n    },\n    lg: {\n        container: \"w-24 h-24\",\n        indicator: \"w-5 h-5\",\n        text: \"text-base\",\n        padding: \"p-4\"\n    }\n};\n/**\n * Componente principal AvailabilityIndicator\n */ const AvailabilityIndicator = (param)=>{\n    let { slotsCount, date, dayName, isSelected = false, onClick, size = \"md\", compact = false, isBlocked = false, blockReason } = param;\n    // CRITICAL FEATURE: Use blocked state if date is blocked, otherwise use availability level\n    const level = isBlocked ? \"blocked\" : getAvailabilityLevel(slotsCount);\n    const config = availabilityConfig[level];\n    const sizeStyles = sizeConfig[size];\n    const IconComponent = config.icon;\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.getDate().toString();\n    };\n    const formatFullDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"es-ES\", {\n            weekday: \"long\",\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const handleClick = ()=>{\n        // Validar que no sea fecha pasada\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const dateObj = new Date(date);\n        dateObj.setHours(0, 0, 0, 0);\n        const isPastDate = dateObj.getTime() < today.getTime();\n        if (onClick && level !== \"none\" && !isPastDate) {\n            onClick();\n        }\n    };\n    // Verificar si es fecha pasada\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const dateObj = new Date(date);\n    dateObj.setHours(0, 0, 0, 0);\n    const isPastDate = dateObj.getTime() < today.getTime();\n    const isClickable = onClick && level !== \"none\" && !isPastDate;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          \".concat(sizeStyles.container, \" \").concat(sizeStyles.padding, \"\\n          \").concat(config.lightColor, \" \").concat(config.borderColor, \"\\n          border-2 rounded-lg\\n          flex flex-col items-center justify-center\\n          transition-all duration-200 ease-in-out\\n          \").concat(isClickable ? \"cursor-pointer hover:shadow-md hover:scale-105\" : \"cursor-default\", \"\\n          \").concat(isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"\", \"\\n          \").concat(level === \"none\" || isPastDate ? \"opacity-60\" : \"\", \"\\n          \").concat(isPastDate ? \"grayscale\" : \"\", \"\\n        \"),\n                onClick: handleClick,\n                role: isClickable ? \"button\" : \"presentation\",\n                tabIndex: isClickable ? 0 : -1,\n                \"aria-label\": \"\".concat(formatFullDate(date), \", \").concat(config.label, \", \").concat(slotsCount, \" horarios disponibles\"),\n                onKeyDown: (e)=>{\n                    if (isClickable && (e.key === \"Enter\" || e.key === \" \")) {\n                        e.preventDefault();\n                        handleClick();\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(config.color, \" \").concat(sizeStyles.indicator, \" rounded-full flex items-center justify-center\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"w-2 h-2 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(sizeStyles.text, \" font-semibold text-gray-900 text-center\"),\n                        children: formatDate(date)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    dayName && !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(sizeStyles.text, \" text-gray-600 text-center leading-tight\"),\n                        children: dayName.substring(0, 3)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined),\n                    !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(sizeStyles.text, \" \").concat(config.textColor, \" text-center font-medium\"),\n                        children: [\n                            slotsCount,\n                            \" slot\",\n                            slotsCount !== 1 ? \"s\" : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: formatFullDate(date)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-300\",\n                        children: config.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-300\",\n                        children: [\n                            slotsCount,\n                            \" horario\",\n                            slotsCount !== 1 ? \"s\" : \"\",\n                            \" disponible\",\n                            slotsCount !== 1 ? \"s\" : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AvailabilityIndicator;\nconst WeeklyAvailability = (param)=>{\n    let { weekData, selectedDate, onDateSelect, size = \"md\" } = param;\n    /**\n   * CRITICAL FIX: Ensure timezone-safe date passing\n   * The day.date should already be timezone-safe from WeeklyAvailabilitySelector,\n   * but we add validation to ensure consistency\n   */ const handleDateClick = (dateString)=>{\n        // DEBUG: Log para verificar fecha antes de enviar\n        console.log(\"=== DEBUG WEEKLY AVAILABILITY CLICK ===\");\n        console.log(\"day.date recibido:\", dateString);\n        // Verificar que la fecha esté en formato correcto YYYY-MM-DD\n        const dateRegex = /^\\d{4}-\\d{2}-\\d{2}$/;\n        if (!dateRegex.test(dateString)) {\n            console.error(\"FORMATO DE FECHA INCORRECTO:\", dateString);\n            return;\n        }\n        // CRITICAL FIX: Verificar consistencia timezone usando parsing seguro\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = dateString.split(\"-\").map(Number);\n        const dateObjSafe = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObjSafe.getFullYear(), \"-\").concat(String(dateObjSafe.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObjSafe.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(dateString); // This creates the problematic UTC interpretation\n        const utcLocalString = \"\".concat(dateObjUTC.getFullYear(), \"-\").concat(String(dateObjUTC.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObjUTC.getDate()).padStart(2, \"0\"));\n        console.log(\"Verificaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - dateString original:\", dateString);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"  - utcLocalString (problematic):\", utcLocalString);\n        console.log(\"  - \\xbfSon iguales? (safe):\", dateString === localDateString);\n        console.log(\"  - \\xbfSon iguales? (UTC):\", dateString === utcLocalString);\n        // CRITICAL FIX: Use timezone-safe comparison for decision\n        if (dateString !== localDateString) {\n            console.warn(\"DESFASE TIMEZONE DETECTADO - usando fecha local corregida\");\n            console.log(\"Enviando fecha corregida:\", localDateString);\n            onDateSelect === null || onDateSelect === void 0 ? void 0 : onDateSelect(localDateString);\n        } else {\n            console.log(\"Fecha consistente (timezone-safe) - enviando original:\", dateString);\n            onDateSelect === null || onDateSelect === void 0 ? void 0 : onDateSelect(dateString);\n        }\n        console.log(\"=========================================\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center space-x-2\",\n        children: weekData.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvailabilityIndicator, {\n                date: day.date,\n                dayName: day.dayName,\n                slotsCount: day.slotsCount,\n                isSelected: selectedDate === day.date,\n                onClick: ()=>handleDateClick(day.date),\n                size: size\n            }, day.date, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\AvailabilityIndicator.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = WeeklyAvailability;\n/**\n * Hook para generar datos de ejemplo de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate)=>{\n    const weekData = [];\n    const dayNames = [\n        \"Domingo\",\n        \"Lunes\",\n        \"Martes\",\n        \"Mi\\xe9rcoles\",\n        \"Jueves\",\n        \"Viernes\",\n        \"S\\xe1bado\"\n    ];\n    for(let i = 0; i < 7; i++){\n        const date = new Date(startDate);\n        date.setDate(startDate.getDate() + i);\n        // Simular disponibilidad variable\n        const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n        const slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n        weekData.push({\n            date: date.toISOString().split(\"T\")[0],\n            dayName: dayNames[date.getDay()],\n            slotsCount\n        });\n    }\n    return weekData;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (AvailabilityIndicator);\nvar _c, _c1;\n$RefreshReg$(_c, \"AvailabilityIndicator\");\n$RefreshReg$(_c1, \"WeeklyAvailability\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\n"));

/***/ })

});