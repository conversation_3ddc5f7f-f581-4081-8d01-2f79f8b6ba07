"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_ai_ChatBot_tsx",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic\n        const utcDateString = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateString;\n        console.log(\"Comparaci\\xf3n timezone:\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString:\", utcDateString);\n        console.log(\"  - localDateString:\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 512,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 522,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 535,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 566,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 565,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 602,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 610,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 628,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 644,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 669,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 510,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"4O/41eNoaHqwK0PAWV/sHVC2lvE=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});