"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_ai_ChatBot_tsx",{

/***/ "(app-pages-browser)/./src/components/appointments/UnifiedAppointmentFlow.tsx":
/*!****************************************************************!*\
  !*** ./src/components/appointments/UnifiedAppointmentFlow.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UnifiedAppointmentFlow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared */ \"(app-pages-browser)/./src/components/appointments/shared/index.ts\");\n/* harmony import */ var _FlowSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlowSelector */ \"(app-pages-browser)/./src/components/appointments/FlowSelector.tsx\");\n/* harmony import */ var _ExpressConfirmation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ExpressConfirmation */ \"(app-pages-browser)/./src/components/appointments/ExpressConfirmation.tsx\");\n/* harmony import */ var _ExpressSearchingState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ExpressSearchingState */ \"(app-pages-browser)/./src/components/appointments/ExpressSearchingState.tsx\");\n/* harmony import */ var _EnhancedTimeSlotSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./EnhancedTimeSlotSelector */ \"(app-pages-browser)/./src/components/appointments/EnhancedTimeSlotSelector.tsx\");\n/* harmony import */ var _WeeklyAvailabilitySelector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./WeeklyAvailabilitySelector */ \"(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\");\n/* harmony import */ var _lib_appointments_OptimalAppointmentFinder__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/appointments/OptimalAppointmentFinder */ \"(app-pages-browser)/./src/lib/appointments/OptimalAppointmentFinder.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * UnifiedAppointmentFlow Component\n * Harmonized appointment booking flow that works for both manual and AI modes\n * Follows PRD2.md specification: Service → Doctor (optional) → Location (optional) → Date → Time → Confirm\n */ \n\n\n\n\n\n\n\n\nfunction UnifiedAppointmentFlow(param) {\n    let { organizationId, userId, patientName, onAppointmentBooked, onCancel, initialData, mode = \"manual\" } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCancelDialog, setShowCancelDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Hybrid flow states\n    const [bookingFlow, setBookingFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optimalAppointment, setOptimalAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSearchingOptimal, setIsSearchingOptimal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Context for enhanced UX\n    const [aiContext, setAiContext] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((initialData === null || initialData === void 0 ? void 0 : initialData.aiContext) || null);\n    // Data states\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [doctors, setDoctors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availability, setAvailability] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Form data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        service_id: (initialData === null || initialData === void 0 ? void 0 : initialData.service_id) || \"\",\n        doctor_id: (initialData === null || initialData === void 0 ? void 0 : initialData.doctor_id) || \"\",\n        location_id: (initialData === null || initialData === void 0 ? void 0 : initialData.location_id) || \"\",\n        appointment_date: (initialData === null || initialData === void 0 ? void 0 : initialData.appointment_date) || \"\",\n        appointment_time: (initialData === null || initialData === void 0 ? void 0 : initialData.appointment_time) || \"\",\n        reason: (initialData === null || initialData === void 0 ? void 0 : initialData.reason) || \"\",\n        notes: (initialData === null || initialData === void 0 ? void 0 : initialData.notes) || \"\"\n    });\n    const [selectedSlot, setSelectedSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Define steps - hybrid flow includes flow selection\n    const getSteps = ()=>{\n        if (bookingFlow === \"express\") {\n            return [\n                {\n                    id: \"service\",\n                    title: \"Seleccionar Servicio\",\n                    completed: false,\n                    current: true\n                },\n                {\n                    id: \"flow\",\n                    title: \"Tipo de Reserva\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"confirm\",\n                    title: \"Confirmar Cita\",\n                    completed: false,\n                    current: false\n                }\n            ];\n        } else if (bookingFlow === \"personalized\") {\n            return [\n                {\n                    id: \"service\",\n                    title: \"Seleccionar Servicio\",\n                    completed: false,\n                    current: true\n                },\n                {\n                    id: \"flow\",\n                    title: \"Tipo de Reserva\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"doctor\",\n                    title: \"Elegir Doctor\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"location\",\n                    title: \"Seleccionar Sede\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"date\",\n                    title: \"Elegir Fecha\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"time\",\n                    title: \"Seleccionar Horario\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"confirm\",\n                    title: \"Confirmar Cita\",\n                    completed: false,\n                    current: false\n                }\n            ];\n        } else {\n            // Default flow before selection\n            return [\n                {\n                    id: \"service\",\n                    title: \"Seleccionar Servicio\",\n                    completed: false,\n                    current: true\n                },\n                {\n                    id: \"flow\",\n                    title: \"Tipo de Reserva\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"doctor\",\n                    title: \"Elegir Doctor\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"location\",\n                    title: \"Seleccionar Sede\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"date\",\n                    title: \"Elegir Fecha\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"time\",\n                    title: \"Seleccionar Horario\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"confirm\",\n                    title: \"Confirmar Cita\",\n                    completed: false,\n                    current: false\n                }\n            ];\n        }\n    };\n    // Update step completion status\n    const updateSteps = ()=>{\n        const steps = getSteps();\n        return steps.map((step, index)=>({\n                ...step,\n                completed: index < currentStep,\n                current: index === currentStep\n            }));\n    };\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadServices();\n        loadLocations();\n    }, [\n        organizationId\n    ]);\n    const loadServices = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/services?organizationId=\".concat(organizationId));\n            if (response.ok) {\n                const data = await response.json();\n                setServices(data.services || []);\n            }\n        } catch (err) {\n            console.error(\"Error loading services:\", err);\n            setError(\"Error al cargar servicios\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadLocations = async ()=>{\n        try {\n            const response = await fetch(\"/api/locations?organizationId=\".concat(organizationId));\n            if (response.ok) {\n                const data = await response.json();\n                setLocations(data.locations || []);\n            }\n        } catch (err) {\n            console.error(\"Error loading locations:\", err);\n        }\n    };\n    const loadDoctors = async (serviceId)=>{\n        try {\n            setLoading(true);\n            let url = \"/api/doctors?organizationId=\".concat(organizationId);\n            if (serviceId) {\n                url += \"&serviceId=\".concat(serviceId);\n            }\n            const response = await fetch(url);\n            if (response.ok) {\n                var _data_data;\n                const data = await response.json();\n                // CRITICAL FIX: API returns data.data, not data.doctors\n                setDoctors(data.data || []);\n                console.log(\"DEBUG: Loaded \".concat(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0, \" doctors for service \").concat(serviceId || \"ALL\"));\n            }\n        } catch (err) {\n            console.error(\"Error loading doctors:\", err);\n            setError(\"Error al cargar doctores\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAvailability = async ()=>{\n        if (!formData.appointment_date) return;\n        try {\n            setLoading(true);\n            let url = \"/api/doctors/availability?organizationId=\".concat(organizationId, \"&date=\").concat(formData.appointment_date);\n            if (formData.doctor_id) {\n                url += \"&doctorId=\".concat(formData.doctor_id);\n            }\n            if (formData.service_id) {\n                url += \"&serviceId=\".concat(formData.service_id);\n            }\n            if (formData.location_id) {\n                url += \"&locationId=\".concat(formData.location_id);\n            }\n            const response = await fetch(url);\n            if (response.ok) {\n                const data = await response.json();\n                setAvailability(data.data || []);\n            }\n        } catch (err) {\n            console.error(\"Error loading availability:\", err);\n            setError(\"Error al cargar disponibilidad\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced availability loader for WeeklyAvailabilitySelector\n    const loadWeeklyAvailability = async (params)=>{\n        try {\n            let url = \"/api/appointments/availability?organizationId=\".concat(params.organizationId, \"&startDate=\").concat(params.startDate, \"&endDate=\").concat(params.endDate);\n            if (params.serviceId) url += \"&serviceId=\".concat(params.serviceId);\n            if (params.doctorId) url += \"&doctorId=\".concat(params.doctorId);\n            if (params.locationId) url += \"&locationId=\".concat(params.locationId);\n            const response = await fetch(url);\n            if (!response.ok) {\n                throw new Error(\"Error \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error || \"Error desconocido al cargar disponibilidad\");\n            }\n            // Process API response into DayAvailabilityData format\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            const today = new Date();\n            const tomorrow = new Date(today);\n            tomorrow.setDate(today.getDate() + 1);\n            const processedData = [];\n            const startDate = new Date(params.startDate);\n            const endDate = new Date(params.endDate);\n            for(let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)){\n                const dateString = date.toISOString().split(\"T\")[0];\n                const dayData = result.data[dateString];\n                const availableSlots = (dayData === null || dayData === void 0 ? void 0 : dayData.availableSlots) || 0;\n                const isToday = date.toDateString() === today.toDateString();\n                const isTomorrow = date.toDateString() === tomorrow.toDateString();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                let availabilityLevel = \"none\";\n                if (availableSlots === 0) availabilityLevel = \"none\";\n                else if (availableSlots <= 2) availabilityLevel = \"low\";\n                else if (availableSlots <= 5) availabilityLevel = \"medium\";\n                else availabilityLevel = \"high\";\n                processedData.push({\n                    date: dateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount: availableSlots,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    slots: (dayData === null || dayData === void 0 ? void 0 : dayData.slots) || []\n                });\n            }\n            return processedData;\n        } catch (error) {\n            console.error(\"Error loading weekly availability:\", error);\n            throw error;\n        }\n    };\n    // Navigation handlers\n    const handleNext = ()=>{\n        const steps = getSteps();\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n            setError(null);\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            const newStep = currentStep - 1;\n            setCurrentStep(newStep);\n            setError(null);\n            // CRITICAL FIX: Reset bookingFlow when returning to flow selection step\n            const steps = getSteps();\n            const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n            if (newStep === flowStepIndex) {\n                console.log(\"DEBUG: Resetting bookingFlow state for flow selection step\");\n                setBookingFlow(null);\n                setOptimalAppointment(null);\n                setIsSearchingOptimal(false);\n                // Clear doctors to force reload when flow is selected again\n                setDoctors([]);\n            }\n        }\n    };\n    const handleCancel = ()=>{\n        setShowCancelDialog(true);\n    };\n    const handleConfirmCancel = ()=>{\n        setShowCancelDialog(false);\n        onCancel === null || onCancel === void 0 ? void 0 : onCancel();\n    };\n    const handleCancelDialog = ()=>{\n        setShowCancelDialog(false);\n    };\n    // Selection handlers\n    const handleServiceSelect = (option)=>{\n        setFormData((prev)=>({\n                ...prev,\n                service_id: option.id\n            }));\n        // Don't load doctors yet - wait for flow selection\n        handleNext();\n    };\n    const handleDoctorSelect = (option)=>{\n        setFormData((prev)=>({\n                ...prev,\n                doctor_id: option.id\n            }));\n        handleNext();\n    };\n    const handleLocationSelect = (option)=>{\n        setFormData((prev)=>({\n                ...prev,\n                location_id: option.id\n            }));\n        handleNext();\n    };\n    const handleDateSelect = (date)=>{\n        setFormData((prev)=>({\n                ...prev,\n                appointment_date: date,\n                appointment_time: \"\"\n            }));\n        setSelectedSlot(null);\n        handleNext();\n    };\n    const handleSlotSelect = (slot)=>{\n        setSelectedSlot(slot);\n        setFormData((prev)=>({\n                ...prev,\n                appointment_time: slot.start_time,\n                doctor_id: slot.doctor_id // Auto-assign doctor if not selected\n            }));\n        handleNext();\n    };\n    // Hybrid flow handlers\n    const handleFlowSelect = async (flowType)=>{\n        setBookingFlow(flowType);\n        if (flowType === \"express\") {\n            // Show searching state and find optimal appointment\n            setIsSearchingOptimal(true);\n            handleNext(); // Move to searching step\n            await findOptimalAppointment();\n        } else {\n            // Continue with personalized flow\n            loadDoctors(formData.service_id);\n            handleNext();\n        }\n    };\n    const findOptimalAppointment = async ()=>{\n        if (!formData.service_id) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // Add minimum delay to show searching animation\n            const searchPromise = new _lib_appointments_OptimalAppointmentFinder__WEBPACK_IMPORTED_MODULE_8__.OptimalAppointmentFinder().findOptimalAppointment({\n                serviceId: formData.service_id,\n                organizationId,\n                preferences: {\n                    maxDaysOut: 14,\n                    timePreference: \"any\"\n                }\n            });\n            // Ensure minimum 8 seconds for UX (matches ExpressSearchingState animation)\n            const [result] = await Promise.all([\n                searchPromise,\n                new Promise((resolve)=>setTimeout(resolve, 8000))\n            ]);\n            if (result) {\n                setOptimalAppointment(result);\n                // Update form data with optimal appointment\n                setFormData((prev)=>({\n                        ...prev,\n                        doctor_id: result.appointment.doctorId,\n                        location_id: result.appointment.locationId,\n                        appointment_date: result.appointment.date,\n                        appointment_time: result.appointment.startTime\n                    }));\n                setIsSearchingOptimal(false);\n                // Skip to confirmation step (step 2 in express flow)\n                setCurrentStep(2);\n            } else {\n                setIsSearchingOptimal(false);\n                setError(\"No se encontraron citas disponibles. Intenta con la reserva personalizada.\");\n                setBookingFlow(\"personalized\");\n                loadDoctors(formData.service_id);\n                setCurrentStep(2); // Go to doctor selection in personalized flow\n            }\n        } catch (err) {\n            console.error(\"Error finding optimal appointment:\", err);\n            setIsSearchingOptimal(false);\n            setError(\"Error al buscar citas disponibles. Intenta con la reserva personalizada.\");\n            setBookingFlow(\"personalized\");\n            loadDoctors(formData.service_id);\n            setCurrentStep(2); // Go to doctor selection in personalized flow\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExpressConfirm = async ()=>{\n        if (!optimalAppointment || !userId) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const bookingData = {\n                organizationId,\n                patientId: userId,\n                doctorId: optimalAppointment.appointment.doctorId,\n                serviceId: formData.service_id,\n                locationId: optimalAppointment.appointment.locationId,\n                appointmentDate: optimalAppointment.appointment.date,\n                startTime: optimalAppointment.appointment.startTime,\n                endTime: optimalAppointment.appointment.endTime,\n                reason: formData.reason,\n                notes: formData.notes || \"Cita agendada via \".concat(mode === \"ai\" ? \"AI Assistant\" : \"Reserva Express\")\n            };\n            const response = await fetch(\"/api/appointments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(bookingData)\n            });\n            const result = await response.json();\n            if (result.success || response.ok) {\n                var _result_data;\n                onAppointmentBooked === null || onAppointmentBooked === void 0 ? void 0 : onAppointmentBooked(result.appointmentId || ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.id));\n            } else {\n                throw new Error(result.error || \"Failed to create appointment\");\n            }\n        } catch (err) {\n            console.error(\"Error booking express appointment:\", err);\n            setError(err instanceof Error ? err.message : \"Error al agendar la cita\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCustomizeFromExpress = ()=>{\n        setBookingFlow(\"personalized\");\n        setOptimalAppointment(null);\n        setIsSearchingOptimal(false);\n        loadDoctors(formData.service_id);\n        setCurrentStep(2); // Go directly to doctor selection in personalized flow\n    };\n    const handleBackToFlowSelection = ()=>{\n        setBookingFlow(null);\n        setOptimalAppointment(null);\n        setIsSearchingOptimal(false);\n        setCurrentStep(1); // Go back to flow selection\n    };\n    const handleCancelExpressSearch = ()=>{\n        setIsSearchingOptimal(false);\n        setBookingFlow(\"personalized\");\n        loadDoctors(formData.service_id);\n        setCurrentStep(2); // Go to doctor selection in personalized flow\n    };\n    // Load availability when date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const steps = getSteps();\n        const timeStepIndex = steps.findIndex((step)=>step.id === \"time\");\n        if (formData.appointment_date && currentStep === timeStepIndex) {\n            loadAvailability();\n        }\n    }, [\n        formData.appointment_date,\n        currentStep,\n        bookingFlow\n    ]);\n    // CRITICAL FIX: Validate state consistency to prevent navigation bugs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const steps = getSteps();\n        const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n        // If we're on flow selection step but bookingFlow is already set, reset it\n        if (currentStep === flowStepIndex && bookingFlow) {\n            console.warn(\"DEBUG: Inconsistent state detected - resetting bookingFlow on flow step\");\n            setBookingFlow(null);\n            setOptimalAppointment(null);\n            setIsSearchingOptimal(false);\n        }\n    }, [\n        currentStep,\n        bookingFlow\n    ]);\n    // Handle appointment booking\n    const handleBookAppointment = async ()=>{\n        if (!selectedSlot || !userId) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const bookingData = {\n                organizationId,\n                patientId: userId,\n                doctorId: selectedSlot.doctor_id,\n                serviceId: formData.service_id,\n                locationId: formData.location_id,\n                appointmentDate: formData.appointment_date,\n                startTime: selectedSlot.start_time,\n                endTime: selectedSlot.end_time,\n                reason: formData.reason,\n                notes: formData.notes || \"Cita agendada via \".concat(mode === \"ai\" ? \"AI Assistant\" : \"formulario manual\")\n            };\n            const response = await fetch(\"/api/appointments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(bookingData)\n            });\n            const result = await response.json();\n            if (result.success || response.ok) {\n                var _result_data;\n                onAppointmentBooked === null || onAppointmentBooked === void 0 ? void 0 : onAppointmentBooked(result.appointmentId || ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.id));\n            } else {\n                throw new Error(result.error || \"Failed to create appointment\");\n            }\n        } catch (err) {\n            console.error(\"Error booking appointment:\", err);\n            setError(err instanceof Error ? err.message : \"Error al agendar la cita\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg max-w-2xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.ProgressIndicator, {\n                        steps: updateSteps(),\n                        currentStep: currentStep\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.AlertMessage, {\n                                type: \"error\",\n                                title: \"Error\",\n                                message: error,\n                                onDismiss: ()=>setError(null),\n                                className: \"mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 11\n                            }, this),\n                            currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.SelectionCard, {\n                                title: \"\\xbfQu\\xe9 tipo de consulta necesitas?\",\n                                subtitle: \"Selecciona el servicio m\\xe9dico que requieres\",\n                                options: services.map((service)=>({\n                                        id: service.id,\n                                        title: service.name,\n                                        description: service.description,\n                                        price: service.price\n                                    })),\n                                selectedId: formData.service_id,\n                                onSelect: handleServiceSelect,\n                                loading: loading,\n                                emptyMessage: \"No hay servicios disponibles\",\n                                gridCols: 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            (()=>{\n                                const steps = getSteps();\n                                const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n                                return currentStep === flowStepIndex && !bookingFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlowSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onFlowSelect: handleFlowSelect,\n                                    loading: loading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n                                return currentStep === flowStepIndex + 1 && bookingFlow === \"express\" && isSearchingOptimal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpressSearchingState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    onCancel: handleCancelExpressSearch\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const doctorStepIndex = steps.findIndex((step)=>step.id === \"doctor\");\n                                return currentStep === doctorStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.SelectionCard, {\n                                    title: \"\\xbfTienes preferencia por alg\\xfan doctor?\",\n                                    subtitle: \"Puedes elegir un doctor espec\\xedfico o continuar para ver disponibilidad general\",\n                                    options: [\n                                        {\n                                            id: \"\",\n                                            title: \"Cualquier doctor disponible\",\n                                            description: \"Ver disponibilidad de todos los doctores (\".concat(doctors.length, \" disponibles)\"),\n                                            subtitle: \"Recomendado para mayor flexibilidad de horarios\"\n                                        },\n                                        ...doctors.map((doctor)=>({\n                                                id: doctor.id,\n                                                title: \"Dr. \".concat(doctor.profiles.first_name, \" \").concat(doctor.profiles.last_name),\n                                                subtitle: doctor.specialization,\n                                                price: doctor.consultation_fee\n                                            }))\n                                    ],\n                                    selectedId: formData.doctor_id,\n                                    onSelect: handleDoctorSelect,\n                                    loading: loading,\n                                    emptyMessage: \"No hay doctores disponibles\",\n                                    gridCols: 1\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const locationStepIndex = steps.findIndex((step)=>step.id === \"location\");\n                                return currentStep === locationStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.SelectionCard, {\n                                    title: \"\\xbfEn qu\\xe9 sede prefieres la consulta?\",\n                                    subtitle: \"Selecciona la ubicaci\\xf3n m\\xe1s conveniente para ti\",\n                                    options: [\n                                        {\n                                            id: \"\",\n                                            title: \"Cualquier sede disponible\",\n                                            description: \"Ver disponibilidad en todas las sedes (\".concat(locations.length, \" disponibles)\"),\n                                            subtitle: \"Recomendado para mayor flexibilidad de horarios\"\n                                        },\n                                        ...locations.map((location)=>({\n                                                id: location.id,\n                                                title: location.name,\n                                                description: location.address\n                                            }))\n                                    ],\n                                    selectedId: formData.location_id,\n                                    onSelect: handleLocationSelect,\n                                    loading: loading,\n                                    emptyMessage: \"No hay sedes disponibles\",\n                                    gridCols: 1\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const dateStepIndex = steps.findIndex((step)=>step.id === \"date\");\n                                // CRITICAL FIX: Detectar modo de edición para permitir navegación semanal flexible\n                                const isEditMode = currentStep < steps.length - 1 && formData.appointment_date;\n                                /**\n           * Calcular fecha mínima apropiada según el contexto\n           * - Modo inicial: Solo fechas futuras (hoy en adelante)\n           * - Modo edición: Permitir fecha ya seleccionada o fechas futuras\n           * NOTA: Esta lógica también afecta la navegación semanal en WeeklyAvailabilitySelector\n           */ const getMinDate = ()=>{\n                                    if (isEditMode) {\n                                        // En modo edición, permitir la fecha ya seleccionada si es anterior a hoy\n                                        const selectedDate = formData.appointment_date;\n                                        const today = new Date().toISOString().split(\"T\")[0];\n                                        // Si hay fecha seleccionada y es anterior a hoy, permitirla para edición\n                                        if (selectedDate && selectedDate < today) {\n                                            return selectedDate;\n                                        }\n                                        // Si no, usar fecha actual como mínimo\n                                        return today;\n                                    }\n                                    // En modo inicial, solo fechas futuras\n                                    return new Date().toISOString().split(\"T\")[0];\n                                };\n                                // DEBUG: Log para verificar minDate dinámico\n                                console.log(\"=== DEBUG MINDATE DIN\\xc1MICO ===\");\n                                console.log(\"isEditMode:\", isEditMode);\n                                console.log(\"currentStep:\", currentStep);\n                                console.log(\"steps.length:\", steps.length);\n                                console.log(\"formData.appointment_date:\", formData.appointment_date);\n                                console.log(\"getMinDate() resultado:\", getMinDate());\n                                console.log(\"================================\");\n                                return currentStep === dateStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyAvailabilitySelector__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    title: \"\\xbfCu\\xe1ndo te gustar\\xeda la cita?\",\n                                    subtitle: \"Selecciona la fecha que mejor te convenga\",\n                                    selectedDate: formData.appointment_date,\n                                    onDateSelect: handleDateSelect,\n                                    organizationId: organizationId,\n                                    serviceId: formData.service_id,\n                                    doctorId: formData.doctor_id,\n                                    locationId: formData.location_id,\n                                    minDate: getMinDate(),\n                                    showDensityIndicators: true,\n                                    enableSmartSuggestions: mode === \"ai\" && !!aiContext,\n                                    aiContext: aiContext || undefined,\n                                    entryMode: mode,\n                                    onLoadAvailability: loadWeeklyAvailability,\n                                    loading: loading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const timeStepIndex = steps.findIndex((step)=>step.id === \"time\");\n                                return currentStep === timeStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedTimeSlotSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    title: \"Horarios disponibles para \".concat(formData.appointment_date),\n                                    subtitle: \"Selecciona el horario que prefieras\",\n                                    slots: availability,\n                                    selectedSlot: selectedSlot,\n                                    onSlotSelect: handleSlotSelect,\n                                    loading: loading,\n                                    emptyMessage: \"No hay horarios disponibles para esta fecha. Intenta con otra fecha.\",\n                                    showDoctorInfo: !formData.doctor_id,\n                                    showPricing: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const confirmStepIndex = steps.findIndex((step)=>step.id === \"confirm\");\n                                return currentStep === confirmStepIndex && bookingFlow === \"express\" && optimalAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpressConfirmation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    appointment: optimalAppointment,\n                                    onConfirm: handleExpressConfirm,\n                                    onCustomize: handleCustomizeFromExpress,\n                                    onBack: handleBackToFlowSelection,\n                                    loading: loading,\n                                    reason: formData.reason,\n                                    notes: formData.notes,\n                                    patientName: patientName,\n                                    onReasonChange: (reason)=>setFormData((prev)=>({\n                                                ...prev,\n                                                reason\n                                            })),\n                                    onNotesChange: (notes)=>setFormData((prev)=>({\n                                                ...prev,\n                                                notes\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                var _services_find, _locations_find;\n                                const steps = getSteps();\n                                const confirmStepIndex = steps.findIndex((step)=>step.id === \"confirm\");\n                                return currentStep === confirmStepIndex && bookingFlow === \"personalized\" && selectedSlot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border-2 border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                    children: \"Confirmar tu cita\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Revisa los detalles y confirma tu cita m\\xe9dica\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.AppointmentSummary, {\n                                            service: (_services_find = services.find((s)=>s.id === formData.service_id)) === null || _services_find === void 0 ? void 0 : _services_find.name,\n                                            doctor: selectedSlot.doctor_name,\n                                            location: (_locations_find = locations.find((l)=>l.id === formData.location_id)) === null || _locations_find === void 0 ? void 0 : _locations_find.name,\n                                            date: formData.appointment_date,\n                                            time: formData.appointment_time,\n                                            specialization: selectedSlot.specialization,\n                                            price: selectedSlot.consultation_fee,\n                                            reason: formData.reason,\n                                            notes: formData.notes,\n                                            patientName: patientName,\n                                            className: \"mb-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Motivo de la consulta (opcional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.reason,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        reason: e.target.value\n                                                                    })),\n                                                            className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Ej: Consulta general, dolor de cabeza, etc.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Notas adicionales (opcional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.notes,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        notes: e.target.value\n                                                                    })),\n                                                            rows: 3,\n                                                            className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Informaci\\xf3n adicional que consideres importante...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 835,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleBookAppointment,\n                                                    disabled: loading,\n                                                    className: \"w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 transition-all duration-300 flex items-center justify-center text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02]\",\n                                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Agendando tu cita...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 mr-2\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Confirmar Cita\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-3\",\n                                                    children: \"Al confirmar, recibir\\xe1s un email con los detalles de tu cita\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, this);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 px-6 py-4 flex justify-between items-center rounded-b-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    currentStep > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleBack,\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors px-3 py-2 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Anterior\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleCancel,\n                                        className: \"flex items-center text-red-600 hover:text-red-700 transition-colors px-3 py-2 rounded-lg hover:bg-red-50 border border-red-200 hover:border-red-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-4 w-4 mr-1\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500 font-medium\",\n                                children: [\n                                    \"Paso \",\n                                    currentStep + 1,\n                                    \" de \",\n                                    getSteps().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 901,\n                                columnNumber: 9\n                            }, this),\n                            currentStep < getSteps().length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleNext,\n                                className: \"flex items-center text-blue-600 hover:text-blue-700 transition-colors px-4 py-2 rounded-lg hover:bg-blue-50 border border-blue-200 hover:border-blue-300 font-medium\",\n                                children: [\n                                    \"Siguiente\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 906,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                        lineNumber: 877,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.ConfirmationDialog, {\n                isOpen: showCancelDialog,\n                title: \"Cancelar reserva de cita\",\n                message: \"\\xbfEst\\xe1s seguro de que quieres cancelar? Se perder\\xe1 toda la informaci\\xf3n ingresada y tendr\\xe1s que empezar de nuevo.\",\n                confirmText: \"S\\xed, cancelar\",\n                cancelText: \"Continuar reservando\",\n                confirmVariant: \"danger\",\n                onConfirm: handleConfirmCancel,\n                onCancel: handleCancelDialog\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                lineNumber: 919,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UnifiedAppointmentFlow, \"nsGTRSDWYbMPpSOHJyh60epgN2o=\");\n_c = UnifiedAppointmentFlow;\nvar _c;\n$RefreshReg$(_c, \"UnifiedAppointmentFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FwcG9pbnRtZW50cy9VbmlmaWVkQXBwb2ludG1lbnRGbG93LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFQTs7OztDQUlDLEdBRWtEO0FBQ0U7QUFrQm5DO0FBQ2tEO0FBQ1o7QUFDSTtBQUNNO0FBQ0k7QUFDZ0Q7QUFHdkcsU0FBU2dCLHVCQUF1QixLQVFyQjtRQVJxQixFQUM3Q0MsY0FBYyxFQUNkQyxNQUFNLEVBQ05DLFdBQVcsRUFDWEMsbUJBQW1CLEVBQ25CQyxRQUFRLEVBQ1JDLFdBQVcsRUFDWEMsT0FBTyxRQUFRLEVBQ1MsR0FScUI7O0lBUzdDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHeEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDeUIsU0FBU0MsV0FBVyxHQUFHMUIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDMkIsT0FBT0MsU0FBUyxHQUFHNUIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQzZCLGtCQUFrQkMsb0JBQW9CLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUV6RCxxQkFBcUI7SUFDckIsTUFBTSxDQUFDK0IsYUFBYUMsZUFBZSxHQUFHaEMsK0NBQVFBLENBQXlCO0lBQ3ZFLE1BQU0sQ0FBQ2lDLG9CQUFvQkMsc0JBQXNCLEdBQUdsQywrQ0FBUUEsQ0FBa0M7SUFDOUYsTUFBTSxDQUFDbUMsb0JBQW9CQyxzQkFBc0IsR0FBR3BDLCtDQUFRQSxDQUFDO0lBRTdELDZCQUE2QjtJQUM3QixNQUFNLENBQUNxQyxXQUFXQyxhQUFhLEdBQUd0QywrQ0FBUUEsQ0FLaENxQixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFnQixTQUFTLEtBQUk7SUFFcEMsY0FBYztJQUNkLE1BQU0sQ0FBQ0UsVUFBVUMsWUFBWSxHQUFHeEMsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUN5QyxTQUFTQyxXQUFXLEdBQUcxQywrQ0FBUUEsQ0FBVyxFQUFFO0lBQ25ELE1BQU0sQ0FBQzJDLFdBQVdDLGFBQWEsR0FBRzVDLCtDQUFRQSxDQUFhLEVBQUU7SUFDekQsTUFBTSxDQUFDNkMsY0FBY0MsZ0JBQWdCLEdBQUc5QywrQ0FBUUEsQ0FBcUIsRUFBRTtJQUV2RSxZQUFZO0lBQ1osTUFBTSxDQUFDK0MsVUFBVUMsWUFBWSxHQUFHaEQsK0NBQVFBLENBQXNCO1FBQzVEaUQsWUFBWTVCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYTRCLFVBQVUsS0FBSTtRQUN2Q0MsV0FBVzdCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYTZCLFNBQVMsS0FBSTtRQUNyQ0MsYUFBYTlCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYThCLFdBQVcsS0FBSTtRQUN6Q0Msa0JBQWtCL0IsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhK0IsZ0JBQWdCLEtBQUk7UUFDbkRDLGtCQUFrQmhDLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWdDLGdCQUFnQixLQUFJO1FBQ25EQyxRQUFRakMsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhaUMsTUFBTSxLQUFJO1FBQy9CQyxPQUFPbEMsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFha0MsS0FBSyxLQUFJO0lBQy9CO0lBRUEsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR3pELCtDQUFRQSxDQUEwQjtJQUUxRSxxREFBcUQ7SUFDckQsTUFBTTBELFdBQVc7UUFDZixJQUFJM0IsZ0JBQWdCLFdBQVc7WUFDN0IsT0FBTztnQkFDTDtvQkFBRTRCLElBQUk7b0JBQVdDLE9BQU87b0JBQXdCQyxXQUFXO29CQUFPQyxTQUFTO2dCQUFLO2dCQUNoRjtvQkFBRUgsSUFBSTtvQkFBUUMsT0FBTztvQkFBbUJDLFdBQVc7b0JBQU9DLFNBQVM7Z0JBQU07Z0JBQ3pFO29CQUFFSCxJQUFJO29CQUFXQyxPQUFPO29CQUFrQkMsV0FBVztvQkFBT0MsU0FBUztnQkFBTTthQUM1RTtRQUNILE9BQU8sSUFBSS9CLGdCQUFnQixnQkFBZ0I7WUFDekMsT0FBTztnQkFDTDtvQkFBRTRCLElBQUk7b0JBQVdDLE9BQU87b0JBQXdCQyxXQUFXO29CQUFPQyxTQUFTO2dCQUFLO2dCQUNoRjtvQkFBRUgsSUFBSTtvQkFBUUMsT0FBTztvQkFBbUJDLFdBQVc7b0JBQU9DLFNBQVM7Z0JBQU07Z0JBQ3pFO29CQUFFSCxJQUFJO29CQUFVQyxPQUFPO29CQUFpQkMsV0FBVztvQkFBT0MsU0FBUztnQkFBTTtnQkFDekU7b0JBQUVILElBQUk7b0JBQVlDLE9BQU87b0JBQW9CQyxXQUFXO29CQUFPQyxTQUFTO2dCQUFNO2dCQUM5RTtvQkFBRUgsSUFBSTtvQkFBUUMsT0FBTztvQkFBZ0JDLFdBQVc7b0JBQU9DLFNBQVM7Z0JBQU07Z0JBQ3RFO29CQUFFSCxJQUFJO29CQUFRQyxPQUFPO29CQUF1QkMsV0FBVztvQkFBT0MsU0FBUztnQkFBTTtnQkFDN0U7b0JBQUVILElBQUk7b0JBQVdDLE9BQU87b0JBQWtCQyxXQUFXO29CQUFPQyxTQUFTO2dCQUFNO2FBQzVFO1FBQ0gsT0FBTztZQUNMLGdDQUFnQztZQUNoQyxPQUFPO2dCQUNMO29CQUFFSCxJQUFJO29CQUFXQyxPQUFPO29CQUF3QkMsV0FBVztvQkFBT0MsU0FBUztnQkFBSztnQkFDaEY7b0JBQUVILElBQUk7b0JBQVFDLE9BQU87b0JBQW1CQyxXQUFXO29CQUFPQyxTQUFTO2dCQUFNO2dCQUN6RTtvQkFBRUgsSUFBSTtvQkFBVUMsT0FBTztvQkFBaUJDLFdBQVc7b0JBQU9DLFNBQVM7Z0JBQU07Z0JBQ3pFO29CQUFFSCxJQUFJO29CQUFZQyxPQUFPO29CQUFvQkMsV0FBVztvQkFBT0MsU0FBUztnQkFBTTtnQkFDOUU7b0JBQUVILElBQUk7b0JBQVFDLE9BQU87b0JBQWdCQyxXQUFXO29CQUFPQyxTQUFTO2dCQUFNO2dCQUN0RTtvQkFBRUgsSUFBSTtvQkFBUUMsT0FBTztvQkFBdUJDLFdBQVc7b0JBQU9DLFNBQVM7Z0JBQU07Z0JBQzdFO29CQUFFSCxJQUFJO29CQUFXQyxPQUFPO29CQUFrQkMsV0FBVztvQkFBT0MsU0FBUztnQkFBTTthQUM1RTtRQUNIO0lBQ0Y7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTUMsY0FBYztRQUNsQixNQUFNQyxRQUFRTjtRQUNkLE9BQU9NLE1BQU1DLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxRQUFXO2dCQUNqQyxHQUFHRCxJQUFJO2dCQUNQTCxXQUFXTSxRQUFRNUM7Z0JBQ25CdUMsU0FBU0ssVUFBVTVDO1lBQ3JCO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEJ0QixnREFBU0EsQ0FBQztRQUNSbUU7UUFDQUM7SUFDRixHQUFHO1FBQUNyRDtLQUFlO0lBRW5CLE1BQU1vRCxlQUFlO1FBQ25CLElBQUk7WUFDRjFDLFdBQVc7WUFDWCxNQUFNNEMsV0FBVyxNQUFNQyxNQUFNLGdDQUErQyxPQUFmdkQ7WUFDN0QsSUFBSXNELFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDbEMsWUFBWWlDLEtBQUtsQyxRQUFRLElBQUksRUFBRTtZQUNqQztRQUNGLEVBQUUsT0FBT29DLEtBQUs7WUFDWkMsUUFBUWpELEtBQUssQ0FBQywyQkFBMkJnRDtZQUN6Qy9DLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTJDLGdCQUFnQjtRQUNwQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGlDQUFnRCxPQUFmdkQ7WUFDOUQsSUFBSXNELFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDOUIsYUFBYTZCLEtBQUs5QixTQUFTLElBQUksRUFBRTtZQUNuQztRQUNGLEVBQUUsT0FBT2dDLEtBQUs7WUFDWkMsUUFBUWpELEtBQUssQ0FBQyw0QkFBNEJnRDtRQUM1QztJQUNGO0lBRUEsTUFBTUUsY0FBYyxPQUFPQztRQUN6QixJQUFJO1lBQ0ZwRCxXQUFXO1lBQ1gsSUFBSXFELE1BQU0sK0JBQThDLE9BQWYvRDtZQUN6QyxJQUFJOEQsV0FBVztnQkFDYkMsT0FBTyxjQUF3QixPQUFWRDtZQUN2QjtZQUVBLE1BQU1SLFdBQVcsTUFBTUMsTUFBTVE7WUFDN0IsSUFBSVQsU0FBU0UsRUFBRSxFQUFFO29CQUljQztnQkFIN0IsTUFBTUEsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQyx3REFBd0Q7Z0JBQ3hEaEMsV0FBVytCLEtBQUtBLElBQUksSUFBSSxFQUFFO2dCQUMxQkcsUUFBUUksR0FBRyxDQUFDLGlCQUErREYsT0FBOUNMLEVBQUFBLGFBQUFBLEtBQUtBLElBQUksY0FBVEEsaUNBQUFBLFdBQVdRLE1BQU0sS0FBSSxHQUFFLHlCQUEwQyxPQUFuQkgsYUFBYTtZQUMxRjtRQUNGLEVBQUUsT0FBT0gsS0FBSztZQUNaQyxRQUFRakQsS0FBSyxDQUFDLDBCQUEwQmdEO1lBQ3hDL0MsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNd0QsbUJBQW1CO1FBQ3ZCLElBQUksQ0FBQ25DLFNBQVNLLGdCQUFnQixFQUFFO1FBRWhDLElBQUk7WUFDRjFCLFdBQVc7WUFDWCxJQUFJcUQsTUFBTSw0Q0FBbUVoQyxPQUF2Qi9CLGdCQUFlLFVBQWtDLE9BQTFCK0IsU0FBU0ssZ0JBQWdCO1lBRXRHLElBQUlMLFNBQVNHLFNBQVMsRUFBRTtnQkFDdEI2QixPQUFPLGFBQWdDLE9BQW5CaEMsU0FBU0csU0FBUztZQUN4QztZQUNBLElBQUlILFNBQVNFLFVBQVUsRUFBRTtnQkFDdkI4QixPQUFPLGNBQWtDLE9BQXBCaEMsU0FBU0UsVUFBVTtZQUMxQztZQUNBLElBQUlGLFNBQVNJLFdBQVcsRUFBRTtnQkFDeEI0QixPQUFPLGVBQW9DLE9BQXJCaEMsU0FBU0ksV0FBVztZQUM1QztZQUVBLE1BQU1tQixXQUFXLE1BQU1DLE1BQU1RO1lBQzdCLElBQUlULFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDNUIsZ0JBQWdCMkIsS0FBS0EsSUFBSSxJQUFJLEVBQUU7WUFDakM7UUFDRixFQUFFLE9BQU9FLEtBQUs7WUFDWkMsUUFBUWpELEtBQUssQ0FBQywrQkFBK0JnRDtZQUM3Qy9DLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsOERBQThEO0lBQzlELE1BQU15RCx5QkFBeUIsT0FBT0M7UUFRcEMsSUFBSTtZQUNGLElBQUlMLE1BQU0saURBQW9GSyxPQUFuQ0EsT0FBT3BFLGNBQWMsRUFBQyxlQUF5Q29FLE9BQTVCQSxPQUFPQyxTQUFTLEVBQUMsYUFBMEIsT0FBZkQsT0FBT0UsT0FBTztZQUV4SSxJQUFJRixPQUFPTixTQUFTLEVBQUVDLE9BQU8sY0FBK0IsT0FBakJLLE9BQU9OLFNBQVM7WUFDM0QsSUFBSU0sT0FBT0csUUFBUSxFQUFFUixPQUFPLGFBQTZCLE9BQWhCSyxPQUFPRyxRQUFRO1lBQ3hELElBQUlILE9BQU9JLFVBQVUsRUFBRVQsT0FBTyxlQUFpQyxPQUFsQkssT0FBT0ksVUFBVTtZQUU5RCxNQUFNbEIsV0FBVyxNQUFNQyxNQUFNUTtZQUM3QixJQUFJLENBQUNULFNBQVNFLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJaUIsTUFBTSxTQUE2Qm5CLE9BQXBCQSxTQUFTb0IsTUFBTSxFQUFDLE1BQXdCLE9BQXBCcEIsU0FBU3FCLFVBQVU7WUFDbEU7WUFFQSxNQUFNQyxTQUFTLE1BQU10QixTQUFTSSxJQUFJO1lBQ2xDLElBQUksQ0FBQ2tCLE9BQU9DLE9BQU8sRUFBRTtnQkFDbkIsTUFBTSxJQUFJSixNQUFNRyxPQUFPakUsS0FBSyxJQUFJO1lBQ2xDO1lBRUEsdURBQXVEO1lBQ3ZELE1BQU1tRSxXQUFXO2dCQUFDO2dCQUFXO2dCQUFTO2dCQUFVO2dCQUFhO2dCQUFVO2dCQUFXO2FBQVM7WUFDM0YsTUFBTUMsUUFBUSxJQUFJQztZQUNsQixNQUFNQyxXQUFXLElBQUlELEtBQUtEO1lBQzFCRSxTQUFTQyxPQUFPLENBQUNILE1BQU1JLE9BQU8sS0FBSztZQUVuQyxNQUFNQyxnQkFBdUMsRUFBRTtZQUMvQyxNQUFNZixZQUFZLElBQUlXLEtBQUtaLE9BQU9DLFNBQVM7WUFDM0MsTUFBTUMsVUFBVSxJQUFJVSxLQUFLWixPQUFPRSxPQUFPO1lBRXZDLElBQUssSUFBSWUsT0FBTyxJQUFJTCxLQUFLWCxZQUFZZ0IsUUFBUWYsU0FBU2UsS0FBS0gsT0FBTyxDQUFDRyxLQUFLRixPQUFPLEtBQUssR0FBSTtnQkFDdEYsTUFBTUcsYUFBYUQsS0FBS0UsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQ25ELE1BQU1DLFVBQVViLE9BQU9uQixJQUFJLENBQUM2QixXQUFXO2dCQUV2QyxNQUFNSSxpQkFBaUJELENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU0MsY0FBYyxLQUFJO2dCQUNsRCxNQUFNQyxVQUFVTixLQUFLTyxZQUFZLE9BQU9iLE1BQU1hLFlBQVk7Z0JBQzFELE1BQU1DLGFBQWFSLEtBQUtPLFlBQVksT0FBT1gsU0FBU1csWUFBWTtnQkFDaEUsTUFBTUUsWUFBWVQsS0FBS1UsTUFBTSxPQUFPLEtBQUtWLEtBQUtVLE1BQU0sT0FBTztnQkFFM0QsSUFBSUMsb0JBQXdEO2dCQUM1RCxJQUFJTixtQkFBbUIsR0FBR00sb0JBQW9CO3FCQUN6QyxJQUFJTixrQkFBa0IsR0FBR00sb0JBQW9CO3FCQUM3QyxJQUFJTixrQkFBa0IsR0FBR00sb0JBQW9CO3FCQUM3Q0Esb0JBQW9CO2dCQUV6QlosY0FBY2EsSUFBSSxDQUFDO29CQUNqQlosTUFBTUM7b0JBQ05ZLFNBQVNwQixRQUFRLENBQUNPLEtBQUtVLE1BQU0sR0FBRztvQkFDaENJLFlBQVlUO29CQUNaTTtvQkFDQUw7b0JBQ0FFO29CQUNBQztvQkFDQU0sT0FBT1gsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTVyxLQUFLLEtBQUksRUFBRTtnQkFDN0I7WUFDRjtZQUVBLE9BQU9oQjtRQUNULEVBQUUsT0FBT3pFLE9BQU87WUFDZGlELFFBQVFqRCxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTTBGLGFBQWE7UUFDakIsTUFBTXJELFFBQVFOO1FBQ2QsSUFBSW5DLGNBQWN5QyxNQUFNaUIsTUFBTSxHQUFHLEdBQUc7WUFDbEN6RCxlQUFlRCxjQUFjO1lBQzdCSyxTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU0wRixhQUFhO1FBQ2pCLElBQUkvRixjQUFjLEdBQUc7WUFDbkIsTUFBTWdHLFVBQVVoRyxjQUFjO1lBQzlCQyxlQUFlK0Y7WUFDZjNGLFNBQVM7WUFFVCx3RUFBd0U7WUFDeEUsTUFBTW9DLFFBQVFOO1lBQ2QsTUFBTThELGdCQUFnQnhELE1BQU15RCxTQUFTLENBQUN2RCxDQUFBQSxPQUFRQSxLQUFLUCxFQUFFLEtBQUs7WUFDMUQsSUFBSTRELFlBQVlDLGVBQWU7Z0JBQzdCNUMsUUFBUUksR0FBRyxDQUFDO2dCQUNaaEQsZUFBZTtnQkFDZkUsc0JBQXNCO2dCQUN0QkUsc0JBQXNCO2dCQUN0Qiw0REFBNEQ7Z0JBQzVETSxXQUFXLEVBQUU7WUFDZjtRQUNGO0lBQ0Y7SUFFQSxNQUFNZ0YsZUFBZTtRQUNuQjVGLG9CQUFvQjtJQUN0QjtJQUVBLE1BQU02RixzQkFBc0I7UUFDMUI3RixvQkFBb0I7UUFDcEJWLHFCQUFBQSwrQkFBQUE7SUFDRjtJQUVBLE1BQU13RyxxQkFBcUI7UUFDekI5RixvQkFBb0I7SUFDdEI7SUFFQSxxQkFBcUI7SUFDckIsTUFBTStGLHNCQUFzQixDQUFDQztRQUMzQjlFLFlBQVkrRSxDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUU5RSxZQUFZNkUsT0FBT25FLEVBQUU7WUFBQztRQUN0RCxtREFBbUQ7UUFDbkQwRDtJQUNGO0lBRUEsTUFBTVcscUJBQXFCLENBQUNGO1FBQzFCOUUsWUFBWStFLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRTdFLFdBQVc0RSxPQUFPbkUsRUFBRTtZQUFDO1FBQ3JEMEQ7SUFDRjtJQUVBLE1BQU1ZLHVCQUF1QixDQUFDSDtRQUM1QjlFLFlBQVkrRSxDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUU1RSxhQUFhMkUsT0FBT25FLEVBQUU7WUFBQztRQUN2RDBEO0lBQ0Y7SUFFQSxNQUFNYSxtQkFBbUIsQ0FBQzdCO1FBQ3hCckQsWUFBWStFLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRTNFLGtCQUFrQmlEO2dCQUFNaEQsa0JBQWtCO1lBQUc7UUFDN0VJLGdCQUFnQjtRQUNoQjREO0lBQ0Y7SUFFQSxNQUFNYyxtQkFBbUIsQ0FBQ0M7UUFDeEIzRSxnQkFBZ0IyRTtRQUNoQnBGLFlBQVkrRSxDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQMUUsa0JBQWtCK0UsS0FBS0MsVUFBVTtnQkFDakNuRixXQUFXa0YsS0FBS2xGLFNBQVMsQ0FBQyxxQ0FBcUM7WUFDakU7UUFDQW1FO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTWlCLG1CQUFtQixPQUFPQztRQUM5QnZHLGVBQWV1RztRQUVmLElBQUlBLGFBQWEsV0FBVztZQUMxQixvREFBb0Q7WUFDcERuRyxzQkFBc0I7WUFDdEJpRixjQUFjLHlCQUF5QjtZQUN2QyxNQUFNbUI7UUFDUixPQUFPO1lBQ0wsa0NBQWtDO1lBQ2xDM0QsWUFBWTlCLFNBQVNFLFVBQVU7WUFDL0JvRTtRQUNGO0lBQ0Y7SUFFQSxNQUFNbUIseUJBQXlCO1FBQzdCLElBQUksQ0FBQ3pGLFNBQVNFLFVBQVUsRUFBRTtRQUUxQixJQUFJO1lBQ0Z2QixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxnREFBZ0Q7WUFDaEQsTUFBTTZHLGdCQUFnQixJQUFJM0gsZ0dBQXdCQSxHQUFHMEgsc0JBQXNCLENBQUM7Z0JBQzFFMUQsV0FBVy9CLFNBQVNFLFVBQVU7Z0JBQzlCakM7Z0JBQ0EwSCxhQUFhO29CQUNYQyxZQUFZO29CQUNaQyxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSw0RUFBNEU7WUFDNUUsTUFBTSxDQUFDaEQsT0FBTyxHQUFHLE1BQU1pRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ2pDTDtnQkFDQSxJQUFJSSxRQUFRRSxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO2FBQzVDO1lBRUQsSUFBSW5ELFFBQVE7Z0JBQ1YxRCxzQkFBc0IwRDtnQkFDdEIsNENBQTRDO2dCQUM1QzVDLFlBQVkrRSxDQUFBQSxPQUFTO3dCQUNuQixHQUFHQSxJQUFJO3dCQUNQN0UsV0FBVzBDLE9BQU9xRCxXQUFXLENBQUMxRCxRQUFRO3dCQUN0Q3BDLGFBQWF5QyxPQUFPcUQsV0FBVyxDQUFDekQsVUFBVTt3QkFDMUNwQyxrQkFBa0J3QyxPQUFPcUQsV0FBVyxDQUFDNUMsSUFBSTt3QkFDekNoRCxrQkFBa0J1QyxPQUFPcUQsV0FBVyxDQUFDQyxTQUFTO29CQUNoRDtnQkFDQTlHLHNCQUFzQjtnQkFDdEIscURBQXFEO2dCQUNyRFosZUFBZTtZQUNqQixPQUFPO2dCQUNMWSxzQkFBc0I7Z0JBQ3RCUixTQUFTO2dCQUNUSSxlQUFlO2dCQUNmNkMsWUFBWTlCLFNBQVNFLFVBQVU7Z0JBQy9CekIsZUFBZSxJQUFJLDhDQUE4QztZQUNuRTtRQUNGLEVBQUUsT0FBT21ELEtBQUs7WUFDWkMsUUFBUWpELEtBQUssQ0FBQyxzQ0FBc0NnRDtZQUNwRHZDLHNCQUFzQjtZQUN0QlIsU0FBUztZQUNUSSxlQUFlO1lBQ2Y2QyxZQUFZOUIsU0FBU0UsVUFBVTtZQUMvQnpCLGVBQWUsSUFBSSw4Q0FBOEM7UUFDbkUsU0FBVTtZQUNSRSxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU15SCx1QkFBdUI7UUFDM0IsSUFBSSxDQUFDbEgsc0JBQXNCLENBQUNoQixRQUFRO1FBRXBDLElBQUk7WUFDRlMsV0FBVztZQUNYRSxTQUFTO1lBRVQsTUFBTXdILGNBQWM7Z0JBQ2xCcEk7Z0JBQ0FxSSxXQUFXcEk7Z0JBQ1hzRSxVQUFVdEQsbUJBQW1CZ0gsV0FBVyxDQUFDMUQsUUFBUTtnQkFDakRULFdBQVcvQixTQUFTRSxVQUFVO2dCQUM5QnVDLFlBQVl2RCxtQkFBbUJnSCxXQUFXLENBQUN6RCxVQUFVO2dCQUNyRDhELGlCQUFpQnJILG1CQUFtQmdILFdBQVcsQ0FBQzVDLElBQUk7Z0JBQ3BENkMsV0FBV2pILG1CQUFtQmdILFdBQVcsQ0FBQ0MsU0FBUztnQkFDbkRLLFNBQVN0SCxtQkFBbUJnSCxXQUFXLENBQUNNLE9BQU87Z0JBQy9DakcsUUFBUVAsU0FBU08sTUFBTTtnQkFDdkJDLE9BQU9SLFNBQVNRLEtBQUssSUFBSSxxQkFBd0UsT0FBbkRqQyxTQUFTLE9BQU8saUJBQWlCO1lBQ2pGO1lBRUEsTUFBTWdELFdBQVcsTUFBTUMsTUFBTSxxQkFBcUI7Z0JBQ2hEaUYsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNSO1lBQ3ZCO1lBRUEsTUFBTXhELFNBQVMsTUFBTXRCLFNBQVNJLElBQUk7WUFFbEMsSUFBSWtCLE9BQU9DLE9BQU8sSUFBSXZCLFNBQVNFLEVBQUUsRUFBRTtvQkFDYW9CO2dCQUE5Q3pFLGdDQUFBQSwwQ0FBQUEsb0JBQXNCeUUsT0FBT2lFLGFBQWEsTUFBSWpFLGVBQUFBLE9BQU9uQixJQUFJLGNBQVhtQixtQ0FBQUEsYUFBYWpDLEVBQUU7WUFDL0QsT0FBTztnQkFDTCxNQUFNLElBQUk4QixNQUFNRyxPQUFPakUsS0FBSyxJQUFJO1lBQ2xDO1FBQ0YsRUFBRSxPQUFPZ0QsS0FBSztZQUNaQyxRQUFRakQsS0FBSyxDQUFDLHNDQUFzQ2dEO1lBQ3BEL0MsU0FBUytDLGVBQWVjLFFBQVFkLElBQUltRixPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNScEksV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNcUksNkJBQTZCO1FBQ2pDL0gsZUFBZTtRQUNmRSxzQkFBc0I7UUFDdEJFLHNCQUFzQjtRQUN0QnlDLFlBQVk5QixTQUFTRSxVQUFVO1FBQy9CekIsZUFBZSxJQUFJLHVEQUF1RDtJQUM1RTtJQUVBLE1BQU13SSw0QkFBNEI7UUFDaENoSSxlQUFlO1FBQ2ZFLHNCQUFzQjtRQUN0QkUsc0JBQXNCO1FBQ3RCWixlQUFlLElBQUksNEJBQTRCO0lBQ2pEO0lBRUEsTUFBTXlJLDRCQUE0QjtRQUNoQzdILHNCQUFzQjtRQUN0QkosZUFBZTtRQUNmNkMsWUFBWTlCLFNBQVNFLFVBQVU7UUFDL0J6QixlQUFlLElBQUksOENBQThDO0lBQ25FO0lBRUEsc0NBQXNDO0lBQ3RDdkIsZ0RBQVNBLENBQUM7UUFDUixNQUFNK0QsUUFBUU47UUFDZCxNQUFNd0csZ0JBQWdCbEcsTUFBTXlELFNBQVMsQ0FBQ3ZELENBQUFBLE9BQVFBLEtBQUtQLEVBQUUsS0FBSztRQUMxRCxJQUFJWixTQUFTSyxnQkFBZ0IsSUFBSTdCLGdCQUFnQjJJLGVBQWU7WUFDOURoRjtRQUNGO0lBQ0YsR0FBRztRQUFDbkMsU0FBU0ssZ0JBQWdCO1FBQUU3QjtRQUFhUTtLQUFZO0lBRXhELHNFQUFzRTtJQUN0RTlCLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTStELFFBQVFOO1FBQ2QsTUFBTThELGdCQUFnQnhELE1BQU15RCxTQUFTLENBQUN2RCxDQUFBQSxPQUFRQSxLQUFLUCxFQUFFLEtBQUs7UUFFMUQsMkVBQTJFO1FBQzNFLElBQUlwQyxnQkFBZ0JpRyxpQkFBaUJ6RixhQUFhO1lBQ2hENkMsUUFBUXVGLElBQUksQ0FBQztZQUNibkksZUFBZTtZQUNmRSxzQkFBc0I7WUFDdEJFLHNCQUFzQjtRQUN4QjtJQUNGLEdBQUc7UUFBQ2I7UUFBYVE7S0FBWTtJQUU3Qiw2QkFBNkI7SUFDN0IsTUFBTXFJLHdCQUF3QjtRQUM1QixJQUFJLENBQUM1RyxnQkFBZ0IsQ0FBQ3ZDLFFBQVE7UUFFOUIsSUFBSTtZQUNGUyxXQUFXO1lBQ1hFLFNBQVM7WUFFVCxNQUFNd0gsY0FBYztnQkFDbEJwSTtnQkFDQXFJLFdBQVdwSTtnQkFDWHNFLFVBQVUvQixhQUFhTixTQUFTO2dCQUNoQzRCLFdBQVcvQixTQUFTRSxVQUFVO2dCQUM5QnVDLFlBQVl6QyxTQUFTSSxXQUFXO2dCQUNoQ21HLGlCQUFpQnZHLFNBQVNLLGdCQUFnQjtnQkFDMUM4RixXQUFXMUYsYUFBYTZFLFVBQVU7Z0JBQ2xDa0IsU0FBUy9GLGFBQWE2RyxRQUFRO2dCQUM5Qi9HLFFBQVFQLFNBQVNPLE1BQU07Z0JBQ3ZCQyxPQUFPUixTQUFTUSxLQUFLLElBQUkscUJBQTBFLE9BQXJEakMsU0FBUyxPQUFPLGlCQUFpQjtZQUNqRjtZQUVBLE1BQU1nRCxXQUFXLE1BQU1DLE1BQU0scUJBQXFCO2dCQUNoRGlGLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDUjtZQUN2QjtZQUVBLE1BQU14RCxTQUFTLE1BQU10QixTQUFTSSxJQUFJO1lBRWxDLElBQUlrQixPQUFPQyxPQUFPLElBQUl2QixTQUFTRSxFQUFFLEVBQUU7b0JBQ2FvQjtnQkFBOUN6RSxnQ0FBQUEsMENBQUFBLG9CQUFzQnlFLE9BQU9pRSxhQUFhLE1BQUlqRSxlQUFBQSxPQUFPbkIsSUFBSSxjQUFYbUIsbUNBQUFBLGFBQWFqQyxFQUFFO1lBQy9ELE9BQU87Z0JBQ0wsTUFBTSxJQUFJOEIsTUFBTUcsT0FBT2pFLEtBQUssSUFBSTtZQUNsQztRQUNGLEVBQUUsT0FBT2dELEtBQUs7WUFDWkMsUUFBUWpELEtBQUssQ0FBQyw4QkFBOEJnRDtZQUM1Qy9DLFNBQVMrQyxlQUFlYyxRQUFRZCxJQUFJbUYsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUnBJLFdBQVc7UUFDYjtJQUNGO0lBRUEscUJBQ0U7OzBCQUNFLDhEQUFDNEk7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDbkssc0RBQWlCQTt3QkFDaEI0RCxPQUFPRDt3QkFDUHhDLGFBQWFBOzs7Ozs7a0NBSWpCLDhEQUFDK0k7d0JBQUlDLFdBQVU7OzRCQUNaNUksdUJBQ0MsOERBQUNyQixpREFBWUE7Z0NBQ1hrSyxNQUFLO2dDQUNMNUcsT0FBTTtnQ0FDTmtHLFNBQVNuSTtnQ0FDVDhJLFdBQVcsSUFBTTdJLFNBQVM7Z0NBQzFCMkksV0FBVTs7Ozs7OzRCQUtiaEosZ0JBQWdCLG1CQUNmLDhEQUFDbEIsa0RBQWFBO2dDQUNadUQsT0FBTTtnQ0FDTjhHLFVBQVM7Z0NBQ1RDLFNBQVNwSSxTQUFTMEIsR0FBRyxDQUFDMkcsQ0FBQUEsVUFBWTt3Q0FDaENqSCxJQUFJaUgsUUFBUWpILEVBQUU7d0NBQ2RDLE9BQU9nSCxRQUFRQyxJQUFJO3dDQUNuQkMsYUFBYUYsUUFBUUUsV0FBVzt3Q0FDaENDLE9BQU9ILFFBQVFHLEtBQUs7b0NBQ3RCO2dDQUNBQyxZQUFZakksU0FBU0UsVUFBVTtnQ0FDL0JnSSxVQUFVcEQ7Z0NBQ1ZwRyxTQUFTQTtnQ0FDVHlKLGNBQWE7Z0NBQ2JDLFVBQVU7Ozs7Ozs0QkFLWjtnQ0FDQSxNQUFNbkgsUUFBUU47Z0NBQ2QsTUFBTThELGdCQUFnQnhELE1BQU15RCxTQUFTLENBQUN2RCxDQUFBQSxPQUFRQSxLQUFLUCxFQUFFLEtBQUs7Z0NBQzFELE9BQU9wQyxnQkFBZ0JpRyxpQkFBaUIsQ0FBQ3pGLDZCQUN2Qyw4REFBQ3RCLHFEQUFZQTtvQ0FDWDJLLGNBQWM5QztvQ0FDZDdHLFNBQVNBOzs7Ozs7NEJBR2Y7NEJBR0U7Z0NBQ0EsTUFBTXVDLFFBQVFOO2dDQUNkLE1BQU04RCxnQkFBZ0J4RCxNQUFNeUQsU0FBUyxDQUFDdkQsQ0FBQUEsT0FBUUEsS0FBS1AsRUFBRSxLQUFLO2dDQUMxRCxPQUFPcEMsZ0JBQWdCaUcsZ0JBQWdCLEtBQUt6RixnQkFBZ0IsYUFBYUksb0NBQ3ZFLDhEQUFDeEIsOERBQXFCQTtvQ0FDcEJTLFVBQVU2STs7Ozs7OzRCQUdoQjs0QkFHRTtnQ0FDQSxNQUFNakcsUUFBUU47Z0NBQ2QsTUFBTTJILGtCQUFrQnJILE1BQU15RCxTQUFTLENBQUN2RCxDQUFBQSxPQUFRQSxLQUFLUCxFQUFFLEtBQUs7Z0NBQzVELE9BQU9wQyxnQkFBZ0I4SixtQkFBbUJ0SixnQkFBZ0IsZ0NBQ3hELDhEQUFDMUIsa0RBQWFBO29DQUNadUQsT0FBTTtvQ0FDTjhHLFVBQVM7b0NBQ1RDLFNBQVM7d0NBQ1A7NENBQ0VoSCxJQUFJOzRDQUNKQyxPQUFPOzRDQUNQa0gsYUFBYSw2Q0FBNEQsT0FBZnJJLFFBQVF3QyxNQUFNLEVBQUM7NENBQ3pFeUYsVUFBVTt3Q0FDWjsyQ0FDR2pJLFFBQVF3QixHQUFHLENBQUNxSCxDQUFBQSxTQUFXO2dEQUN4QjNILElBQUkySCxPQUFPM0gsRUFBRTtnREFDYkMsT0FBTyxPQUFxQzBILE9BQTlCQSxPQUFPQyxRQUFRLENBQUNDLFVBQVUsRUFBQyxLQUE2QixPQUExQkYsT0FBT0MsUUFBUSxDQUFDRSxTQUFTO2dEQUNyRWYsVUFBVVksT0FBT0ksY0FBYztnREFDL0JYLE9BQU9PLE9BQU9LLGdCQUFnQjs0Q0FDaEM7cUNBQ0Q7b0NBQ0RYLFlBQVlqSSxTQUFTRyxTQUFTO29DQUM5QitILFVBQVVqRDtvQ0FDVnZHLFNBQVNBO29DQUNUeUosY0FBYTtvQ0FDYkMsVUFBVTs7Ozs7OzRCQUdoQjs0QkFHRTtnQ0FDQSxNQUFNbkgsUUFBUU47Z0NBQ2QsTUFBTWtJLG9CQUFvQjVILE1BQU15RCxTQUFTLENBQUN2RCxDQUFBQSxPQUFRQSxLQUFLUCxFQUFFLEtBQUs7Z0NBQzlELE9BQU9wQyxnQkFBZ0JxSyxxQkFBcUI3SixnQkFBZ0IsZ0NBQzFELDhEQUFDMUIsa0RBQWFBO29DQUNadUQsT0FBTTtvQ0FDTjhHLFVBQVM7b0NBQ1RDLFNBQVM7d0NBQ1A7NENBQ0VoSCxJQUFJOzRDQUNKQyxPQUFPOzRDQUNQa0gsYUFBYSwwQ0FBMkQsT0FBakJuSSxVQUFVc0MsTUFBTSxFQUFDOzRDQUN4RXlGLFVBQVU7d0NBQ1o7MkNBQ0cvSCxVQUFVc0IsR0FBRyxDQUFDNEgsQ0FBQUEsV0FBYTtnREFDNUJsSSxJQUFJa0ksU0FBU2xJLEVBQUU7Z0RBQ2ZDLE9BQU9pSSxTQUFTaEIsSUFBSTtnREFDcEJDLGFBQWFlLFNBQVNDLE9BQU87NENBQy9CO3FDQUNEO29DQUNEZCxZQUFZakksU0FBU0ksV0FBVztvQ0FDaEM4SCxVQUFVaEQ7b0NBQ1Z4RyxTQUFTQTtvQ0FDVHlKLGNBQWE7b0NBQ2JDLFVBQVU7Ozs7Ozs0QkFHaEI7NEJBR0U7Z0NBQ0EsTUFBTW5ILFFBQVFOO2dDQUNkLE1BQU1xSSxnQkFBZ0IvSCxNQUFNeUQsU0FBUyxDQUFDdkQsQ0FBQUEsT0FBUUEsS0FBS1AsRUFBRSxLQUFLO2dDQUUxRCxtRkFBbUY7Z0NBQ25GLE1BQU1xSSxhQUFhekssY0FBY3lDLE1BQU1pQixNQUFNLEdBQUcsS0FBS2xDLFNBQVNLLGdCQUFnQjtnQ0FFOUU7Ozs7O1dBS0MsR0FDRCxNQUFNNkksYUFBYTtvQ0FDakIsSUFBSUQsWUFBWTt3Q0FDZCwwRUFBMEU7d0NBQzFFLE1BQU1FLGVBQWVuSixTQUFTSyxnQkFBZ0I7d0NBQzlDLE1BQU0yQyxRQUFRLElBQUlDLE9BQU9PLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO3dDQUVwRCx5RUFBeUU7d0NBQ3pFLElBQUkwRixnQkFBZ0JBLGVBQWVuRyxPQUFPOzRDQUN4QyxPQUFPbUc7d0NBQ1Q7d0NBRUEsdUNBQXVDO3dDQUN2QyxPQUFPbkc7b0NBQ1Q7b0NBRUEsdUNBQXVDO29DQUN2QyxPQUFPLElBQUlDLE9BQU9PLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dDQUMvQztnQ0FFQSw2Q0FBNkM7Z0NBQzdDNUIsUUFBUUksR0FBRyxDQUFDO2dDQUNaSixRQUFRSSxHQUFHLENBQUMsZUFBZWdIO2dDQUMzQnBILFFBQVFJLEdBQUcsQ0FBQyxnQkFBZ0J6RDtnQ0FDNUJxRCxRQUFRSSxHQUFHLENBQUMsaUJBQWlCaEIsTUFBTWlCLE1BQU07Z0NBQ3pDTCxRQUFRSSxHQUFHLENBQUMsOEJBQThCakMsU0FBU0ssZ0JBQWdCO2dDQUNuRXdCLFFBQVFJLEdBQUcsQ0FBQywyQkFBMkJpSDtnQ0FDdkNySCxRQUFRSSxHQUFHLENBQUM7Z0NBRVosT0FBT3pELGdCQUFnQndLLGlCQUFpQmhLLGdCQUFnQixnQ0FDdEQsOERBQUNsQixtRUFBMEJBO29DQUN6QitDLE9BQU07b0NBQ044RyxVQUFTO29DQUNUd0IsY0FBY25KLFNBQVNLLGdCQUFnQjtvQ0FDdkMrSSxjQUFjakU7b0NBQ2RsSCxnQkFBZ0JBO29DQUNoQjhELFdBQVcvQixTQUFTRSxVQUFVO29DQUM5QnNDLFVBQVV4QyxTQUFTRyxTQUFTO29DQUM1QnNDLFlBQVl6QyxTQUFTSSxXQUFXO29DQUNoQ2lKLFNBQVNIO29DQUNUSSx1QkFBdUI7b0NBQ3ZCQyx3QkFBd0JoTCxTQUFTLFFBQVEsQ0FBQyxDQUFDZTtvQ0FDM0NBLFdBQVdBLGFBQWFrSztvQ0FDeEJDLFdBQVdsTDtvQ0FDWG1MLG9CQUFvQnRIO29DQUNwQjFELFNBQVNBOzs7Ozs7NEJBR2Y7NEJBR0U7Z0NBQ0EsTUFBTXVDLFFBQVFOO2dDQUNkLE1BQU13RyxnQkFBZ0JsRyxNQUFNeUQsU0FBUyxDQUFDdkQsQ0FBQUEsT0FBUUEsS0FBS1AsRUFBRSxLQUFLO2dDQUMxRCxPQUFPcEMsZ0JBQWdCMkksaUJBQWlCbkksZ0JBQWdCLGdDQUN0RCw4REFBQ25CLGlFQUF3QkE7b0NBQ3ZCZ0QsT0FBTyw2QkFBdUQsT0FBMUJiLFNBQVNLLGdCQUFnQjtvQ0FDN0RzSCxVQUFTO29DQUNUdEQsT0FBT3ZFO29DQUNQVyxjQUFjQTtvQ0FDZGtKLGNBQWN2RTtvQ0FDZDFHLFNBQVNBO29DQUNUeUosY0FBYTtvQ0FDYnlCLGdCQUFnQixDQUFDNUosU0FBU0csU0FBUztvQ0FDbkMwSixhQUFhOzs7Ozs7NEJBR25COzRCQUdFO2dDQUNBLE1BQU01SSxRQUFRTjtnQ0FDZCxNQUFNbUosbUJBQW1CN0ksTUFBTXlELFNBQVMsQ0FBQ3ZELENBQUFBLE9BQVFBLEtBQUtQLEVBQUUsS0FBSztnQ0FDN0QsT0FBT3BDLGdCQUFnQnNMLG9CQUFvQjlLLGdCQUFnQixhQUFhRSxvQ0FDdEUsOERBQUN2Qiw0REFBbUJBO29DQUNsQnVJLGFBQWFoSDtvQ0FDYjZLLFdBQVczRDtvQ0FDWDRELGFBQWFoRDtvQ0FDYmlELFFBQVFoRDtvQ0FDUnZJLFNBQVNBO29DQUNUNkIsUUFBUVAsU0FBU08sTUFBTTtvQ0FDdkJDLE9BQU9SLFNBQVNRLEtBQUs7b0NBQ3JCckMsYUFBYUE7b0NBQ2IrTCxnQkFBZ0IsQ0FBQzNKLFNBQVdOLFlBQVkrRSxDQUFBQSxPQUFTO2dEQUFFLEdBQUdBLElBQUk7Z0RBQUV6RTs0Q0FBTztvQ0FDbkU0SixlQUFlLENBQUMzSixRQUFVUCxZQUFZK0UsQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFeEU7NENBQU07Ozs7Ozs0QkFHdEU7NEJBR0U7b0NBZ0JlaEIsZ0JBRUNJO2dDQWpCaEIsTUFBTXFCLFFBQVFOO2dDQUNkLE1BQU1tSixtQkFBbUI3SSxNQUFNeUQsU0FBUyxDQUFDdkQsQ0FBQUEsT0FBUUEsS0FBS1AsRUFBRSxLQUFLO2dDQUM3RCxPQUFPcEMsZ0JBQWdCc0wsb0JBQW9COUssZ0JBQWdCLGtCQUFrQnlCLDhCQUMzRSw4REFBQzhHO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQzRDO3dEQUFJNUMsV0FBVTt3REFBcUI2QyxNQUFLO3dEQUFPQyxRQUFPO3dEQUFlQyxTQUFRO2tFQUM1RSw0RUFBQ0M7NERBQUtDLGVBQWM7NERBQVFDLGdCQUFlOzREQUFRQyxhQUFhOzREQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUd6RSw4REFBQ0M7b0RBQUdyRCxXQUFVOzhEQUF3Qzs7Ozs7OzhEQUN0RCw4REFBQ3NEO29EQUFFdEQsV0FBVTs4REFBZ0I7Ozs7Ozs7Ozs7OztzREFHL0IsOERBQUNoSyx1REFBa0JBOzRDQUNqQnFLLE9BQU8sR0FBRXJJLGlCQUFBQSxTQUFTdUwsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFcEssRUFBRSxLQUFLWixTQUFTRSxVQUFVLGVBQS9DVixxQ0FBQUEsZUFBa0RzSSxJQUFJOzRDQUMvRFMsUUFBUTlILGFBQWF3SyxXQUFXOzRDQUNoQ25DLFFBQVEsR0FBRWxKLGtCQUFBQSxVQUFVbUwsSUFBSSxDQUFDRyxDQUFBQSxJQUFLQSxFQUFFdEssRUFBRSxLQUFLWixTQUFTSSxXQUFXLGVBQWpEUixzQ0FBQUEsZ0JBQW9Ea0ksSUFBSTs0Q0FDbEV4RSxNQUFNdEQsU0FBU0ssZ0JBQWdCOzRDQUMvQjhLLE1BQU1uTCxTQUFTTSxnQkFBZ0I7NENBQy9CcUksZ0JBQWdCbEksYUFBYWtJLGNBQWM7NENBQzNDWCxPQUFPdkgsYUFBYW1JLGdCQUFnQjs0Q0FDcENySSxRQUFRUCxTQUFTTyxNQUFNOzRDQUN2QkMsT0FBT1IsU0FBU1EsS0FBSzs0Q0FDckJyQyxhQUFhQTs0Q0FDYnFKLFdBQVU7Ozs7OztzREFJWiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUM2RDs0REFBTTVELFdBQVU7c0VBQStDOzs7Ozs7c0VBR2hFLDhEQUFDNkQ7NERBQ0M1RCxNQUFLOzREQUNMNkQsT0FBT3RMLFNBQVNPLE1BQU07NERBQ3RCZ0wsVUFBVSxDQUFDQyxJQUFNdkwsWUFBWStFLENBQUFBLE9BQVM7d0VBQUUsR0FBR0EsSUFBSTt3RUFBRXpFLFFBQVFpTCxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0VBQUM7NERBQ3hFOUQsV0FBVTs0REFDVmtFLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFJaEIsOERBQUNuRTs7c0VBQ0MsOERBQUM2RDs0REFBTTVELFdBQVU7c0VBQStDOzs7Ozs7c0VBR2hFLDhEQUFDbUU7NERBQ0NMLE9BQU90TCxTQUFTUSxLQUFLOzREQUNyQitLLFVBQVUsQ0FBQ0MsSUFBTXZMLFlBQVkrRSxDQUFBQSxPQUFTO3dFQUFFLEdBQUdBLElBQUk7d0VBQUV4RSxPQUFPZ0wsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29FQUFDOzREQUN2RU0sTUFBTTs0REFDTnBFLFdBQVU7NERBQ1ZrRSxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTWxCLDhEQUFDbkU7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDcUU7b0RBQ0NwRSxNQUFLO29EQUNMcUUsU0FBU3pFO29EQUNUMEUsVUFBVXJOO29EQUNWOEksV0FBVTs4REFFVDlJLHdCQUNDOzswRUFDRSw4REFBQzZJO2dFQUFJQyxXQUFVOzs7Ozs7NERBQXVFOztxRkFJeEY7OzBFQUNFLDhEQUFDNEM7Z0VBQUk1QyxXQUFVO2dFQUFlNkMsTUFBSztnRUFBT0MsUUFBTztnRUFBZUMsU0FBUTswRUFDdEUsNEVBQUNDO29FQUFLQyxlQUFjO29FQUFRQyxnQkFBZTtvRUFBUUMsYUFBYTtvRUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7NERBQ2pFOzs7Ozs7Ozs4REFLWiw4REFBQ0U7b0RBQUV0RCxXQUFVOzhEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU1sRDs7Ozs7OztrQ0FJRiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1poSixjQUFjLG1CQUNiLDhEQUFDcU47d0NBQ0NwRSxNQUFLO3dDQUNMcUUsU0FBU3ZIO3dDQUNUaUQsV0FBVTs7MERBRVYsOERBQUNySyxnR0FBU0E7Z0RBQUNxSyxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7O2tEQUkxQyw4REFBQ3FFO3dDQUNDcEUsTUFBSzt3Q0FDTHFFLFNBQVNuSDt3Q0FDVDZDLFdBQVU7OzBEQUVWLDhEQUFDNEM7Z0RBQUk1QyxXQUFVO2dEQUFlNkMsTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDdEUsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7NENBQ2pFOzs7Ozs7Ozs7Ozs7OzBDQUtWLDhEQUFDckQ7Z0NBQUlDLFdBQVU7O29DQUFvQztvQ0FDM0NoSixjQUFjO29DQUFFO29DQUFLbUMsV0FBV3VCLE1BQU07Ozs7Ozs7NEJBRzdDMUQsY0FBY21DLFdBQVd1QixNQUFNLEdBQUcsbUJBQ2pDLDhEQUFDMko7Z0NBQ0NwRSxNQUFLO2dDQUNMcUUsU0FBU3hIO2dDQUNUa0QsV0FBVTs7b0NBQ1g7a0RBRUMsOERBQUNwSyxpR0FBVUE7d0NBQUNvSyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzlCLDhEQUFDL0osdURBQWtCQTtnQkFDakJ1TyxRQUFRbE47Z0JBQ1IrQixPQUFNO2dCQUNOa0csU0FBUTtnQkFDUmtGLGFBQVk7Z0JBQ1pDLFlBQVc7Z0JBQ1hDLGdCQUFlO2dCQUNmcEMsV0FBV25GO2dCQUNYdkcsVUFBVXdHOzs7Ozs7OztBQUloQjtHQTkzQndCN0c7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvYXBwb2ludG1lbnRzL1VuaWZpZWRBcHBvaW50bWVudEZsb3cudHN4PzRkZTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG4vKipcbiAqIFVuaWZpZWRBcHBvaW50bWVudEZsb3cgQ29tcG9uZW50XG4gKiBIYXJtb25pemVkIGFwcG9pbnRtZW50IGJvb2tpbmcgZmxvdyB0aGF0IHdvcmtzIGZvciBib3RoIG1hbnVhbCBhbmQgQUkgbW9kZXNcbiAqIEZvbGxvd3MgUFJEMi5tZCBzcGVjaWZpY2F0aW9uOiBTZXJ2aWNlIOKGkiBEb2N0b3IgKG9wdGlvbmFsKSDihpIgTG9jYXRpb24gKG9wdGlvbmFsKSDihpIgRGF0ZSDihpIgVGltZSDihpIgQ29uZmlybVxuICovXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQXJyb3dMZWZ0LCBBcnJvd1JpZ2h0IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7XG4gIFByb2dyZXNzSW5kaWNhdG9yLFxuICBTZWxlY3Rpb25DYXJkLFxuICBBbGVydE1lc3NhZ2UsXG4gIERhdGVTZWxlY3RvcixcbiAgVGltZVNsb3RTZWxlY3RvcixcbiAgQXBwb2ludG1lbnRTdW1tYXJ5LFxuICBMb2FkaW5nU3RhdGUsXG4gIENvbmZpcm1hdGlvbkRpYWxvZyxcbiAgdHlwZSBGbG93U3RlcCxcbiAgdHlwZSBBcHBvaW50bWVudEZvcm1EYXRhLFxuICB0eXBlIEFwcG9pbnRtZW50Qm9va2luZ1Byb3BzLFxuICB0eXBlIFNlbGVjdGlvbk9wdGlvbixcbiAgdHlwZSBTZXJ2aWNlLFxuICB0eXBlIERvY3RvcixcbiAgdHlwZSBMb2NhdGlvbixcbiAgdHlwZSBBdmFpbGFiaWxpdHlTbG90XG59IGZyb20gJy4vc2hhcmVkJztcbmltcG9ydCBGbG93U2VsZWN0b3IsIHsgdHlwZSBCb29raW5nRmxvd1R5cGUgfSBmcm9tICcuL0Zsb3dTZWxlY3Rvcic7XG5pbXBvcnQgRXhwcmVzc0NvbmZpcm1hdGlvbiBmcm9tICcuL0V4cHJlc3NDb25maXJtYXRpb24nO1xuaW1wb3J0IEV4cHJlc3NTZWFyY2hpbmdTdGF0ZSBmcm9tICcuL0V4cHJlc3NTZWFyY2hpbmdTdGF0ZSc7XG5pbXBvcnQgRW5oYW5jZWRUaW1lU2xvdFNlbGVjdG9yIGZyb20gJy4vRW5oYW5jZWRUaW1lU2xvdFNlbGVjdG9yJztcbmltcG9ydCBXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvciBmcm9tICcuL1dlZWtseUF2YWlsYWJpbGl0eVNlbGVjdG9yJztcbmltcG9ydCB7IE9wdGltYWxBcHBvaW50bWVudEZpbmRlciwgdHlwZSBPcHRpbWFsQXBwb2ludG1lbnRSZXN1bHQgfSBmcm9tICdAL2xpYi9hcHBvaW50bWVudHMvT3B0aW1hbEFwcG9pbnRtZW50RmluZGVyJztcbmltcG9ydCB7IHVzZVdlZWtseUF2YWlsYWJpbGl0eSwgdHlwZSBEYXlBdmFpbGFiaWxpdHlEYXRhIH0gZnJvbSAnQC9ob29rcy91c2VBdmFpbGFiaWxpdHlEYXRhJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVW5pZmllZEFwcG9pbnRtZW50Rmxvdyh7XG4gIG9yZ2FuaXphdGlvbklkLFxuICB1c2VySWQsXG4gIHBhdGllbnROYW1lLFxuICBvbkFwcG9pbnRtZW50Qm9va2VkLFxuICBvbkNhbmNlbCxcbiAgaW5pdGlhbERhdGEsXG4gIG1vZGUgPSAnbWFudWFsJ1xufTogQXBwb2ludG1lbnRCb29raW5nUHJvcHMpIHtcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2hvd0NhbmNlbERpYWxvZywgc2V0U2hvd0NhbmNlbERpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gSHlicmlkIGZsb3cgc3RhdGVzXG4gIGNvbnN0IFtib29raW5nRmxvdywgc2V0Qm9va2luZ0Zsb3ddID0gdXNlU3RhdGU8Qm9va2luZ0Zsb3dUeXBlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtvcHRpbWFsQXBwb2ludG1lbnQsIHNldE9wdGltYWxBcHBvaW50bWVudF0gPSB1c2VTdGF0ZTxPcHRpbWFsQXBwb2ludG1lbnRSZXN1bHQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzU2VhcmNoaW5nT3B0aW1hbCwgc2V0SXNTZWFyY2hpbmdPcHRpbWFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBBSSBDb250ZXh0IGZvciBlbmhhbmNlZCBVWFxuICBjb25zdCBbYWlDb250ZXh0LCBzZXRBaUNvbnRleHRdID0gdXNlU3RhdGU8e1xuICAgIHN1Z2dlc3RlZERhdGVzPzogc3RyaW5nW107XG4gICAgcHJlZmVycmVkVGltZVJhbmdlPzogJ21vcm5pbmcnIHwgJ2FmdGVybm9vbicgfCAnZXZlbmluZyc7XG4gICAgdXJnZW5jeUxldmVsPzogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcbiAgICBleHRyYWN0ZWRQcmVmZXJlbmNlcz86IGFueTtcbiAgfSB8IG51bGw+KGluaXRpYWxEYXRhPy5haUNvbnRleHQgfHwgbnVsbCk7XG5cbiAgLy8gRGF0YSBzdGF0ZXNcbiAgY29uc3QgW3NlcnZpY2VzLCBzZXRTZXJ2aWNlc10gPSB1c2VTdGF0ZTxTZXJ2aWNlW10+KFtdKTtcbiAgY29uc3QgW2RvY3RvcnMsIHNldERvY3RvcnNdID0gdXNlU3RhdGU8RG9jdG9yW10+KFtdKTtcbiAgY29uc3QgW2xvY2F0aW9ucywgc2V0TG9jYXRpb25zXSA9IHVzZVN0YXRlPExvY2F0aW9uW10+KFtdKTtcbiAgY29uc3QgW2F2YWlsYWJpbGl0eSwgc2V0QXZhaWxhYmlsaXR5XSA9IHVzZVN0YXRlPEF2YWlsYWJpbGl0eVNsb3RbXT4oW10pO1xuXG4gIC8vIEZvcm0gZGF0YVxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlPEFwcG9pbnRtZW50Rm9ybURhdGE+KHtcbiAgICBzZXJ2aWNlX2lkOiBpbml0aWFsRGF0YT8uc2VydmljZV9pZCB8fCAnJyxcbiAgICBkb2N0b3JfaWQ6IGluaXRpYWxEYXRhPy5kb2N0b3JfaWQgfHwgJycsXG4gICAgbG9jYXRpb25faWQ6IGluaXRpYWxEYXRhPy5sb2NhdGlvbl9pZCB8fCAnJyxcbiAgICBhcHBvaW50bWVudF9kYXRlOiBpbml0aWFsRGF0YT8uYXBwb2ludG1lbnRfZGF0ZSB8fCAnJyxcbiAgICBhcHBvaW50bWVudF90aW1lOiBpbml0aWFsRGF0YT8uYXBwb2ludG1lbnRfdGltZSB8fCAnJyxcbiAgICByZWFzb246IGluaXRpYWxEYXRhPy5yZWFzb24gfHwgJycsXG4gICAgbm90ZXM6IGluaXRpYWxEYXRhPy5ub3RlcyB8fCAnJ1xuICB9KTtcblxuICBjb25zdCBbc2VsZWN0ZWRTbG90LCBzZXRTZWxlY3RlZFNsb3RdID0gdXNlU3RhdGU8QXZhaWxhYmlsaXR5U2xvdCB8IG51bGw+KG51bGwpO1xuXG4gIC8vIERlZmluZSBzdGVwcyAtIGh5YnJpZCBmbG93IGluY2x1ZGVzIGZsb3cgc2VsZWN0aW9uXG4gIGNvbnN0IGdldFN0ZXBzID0gKCk6IEZsb3dTdGVwW10gPT4ge1xuICAgIGlmIChib29raW5nRmxvdyA9PT0gJ2V4cHJlc3MnKSB7XG4gICAgICByZXR1cm4gW1xuICAgICAgICB7IGlkOiAnc2VydmljZScsIHRpdGxlOiAnU2VsZWNjaW9uYXIgU2VydmljaW8nLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiB0cnVlIH0sXG4gICAgICAgIHsgaWQ6ICdmbG93JywgdGl0bGU6ICdUaXBvIGRlIFJlc2VydmEnLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9LFxuICAgICAgICB7IGlkOiAnY29uZmlybScsIHRpdGxlOiAnQ29uZmlybWFyIENpdGEnLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9XG4gICAgICBdO1xuICAgIH0gZWxzZSBpZiAoYm9va2luZ0Zsb3cgPT09ICdwZXJzb25hbGl6ZWQnKSB7XG4gICAgICByZXR1cm4gW1xuICAgICAgICB7IGlkOiAnc2VydmljZScsIHRpdGxlOiAnU2VsZWNjaW9uYXIgU2VydmljaW8nLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiB0cnVlIH0sXG4gICAgICAgIHsgaWQ6ICdmbG93JywgdGl0bGU6ICdUaXBvIGRlIFJlc2VydmEnLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9LFxuICAgICAgICB7IGlkOiAnZG9jdG9yJywgdGl0bGU6ICdFbGVnaXIgRG9jdG9yJywgY29tcGxldGVkOiBmYWxzZSwgY3VycmVudDogZmFsc2UgfSxcbiAgICAgICAgeyBpZDogJ2xvY2F0aW9uJywgdGl0bGU6ICdTZWxlY2Npb25hciBTZWRlJywgY29tcGxldGVkOiBmYWxzZSwgY3VycmVudDogZmFsc2UgfSxcbiAgICAgICAgeyBpZDogJ2RhdGUnLCB0aXRsZTogJ0VsZWdpciBGZWNoYScsIGNvbXBsZXRlZDogZmFsc2UsIGN1cnJlbnQ6IGZhbHNlIH0sXG4gICAgICAgIHsgaWQ6ICd0aW1lJywgdGl0bGU6ICdTZWxlY2Npb25hciBIb3JhcmlvJywgY29tcGxldGVkOiBmYWxzZSwgY3VycmVudDogZmFsc2UgfSxcbiAgICAgICAgeyBpZDogJ2NvbmZpcm0nLCB0aXRsZTogJ0NvbmZpcm1hciBDaXRhJywgY29tcGxldGVkOiBmYWxzZSwgY3VycmVudDogZmFsc2UgfVxuICAgICAgXTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRGVmYXVsdCBmbG93IGJlZm9yZSBzZWxlY3Rpb25cbiAgICAgIHJldHVybiBbXG4gICAgICAgIHsgaWQ6ICdzZXJ2aWNlJywgdGl0bGU6ICdTZWxlY2Npb25hciBTZXJ2aWNpbycsIGNvbXBsZXRlZDogZmFsc2UsIGN1cnJlbnQ6IHRydWUgfSxcbiAgICAgICAgeyBpZDogJ2Zsb3cnLCB0aXRsZTogJ1RpcG8gZGUgUmVzZXJ2YScsIGNvbXBsZXRlZDogZmFsc2UsIGN1cnJlbnQ6IGZhbHNlIH0sXG4gICAgICAgIHsgaWQ6ICdkb2N0b3InLCB0aXRsZTogJ0VsZWdpciBEb2N0b3InLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9LFxuICAgICAgICB7IGlkOiAnbG9jYXRpb24nLCB0aXRsZTogJ1NlbGVjY2lvbmFyIFNlZGUnLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9LFxuICAgICAgICB7IGlkOiAnZGF0ZScsIHRpdGxlOiAnRWxlZ2lyIEZlY2hhJywgY29tcGxldGVkOiBmYWxzZSwgY3VycmVudDogZmFsc2UgfSxcbiAgICAgICAgeyBpZDogJ3RpbWUnLCB0aXRsZTogJ1NlbGVjY2lvbmFyIEhvcmFyaW8nLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9LFxuICAgICAgICB7IGlkOiAnY29uZmlybScsIHRpdGxlOiAnQ29uZmlybWFyIENpdGEnLCBjb21wbGV0ZWQ6IGZhbHNlLCBjdXJyZW50OiBmYWxzZSB9XG4gICAgICBdO1xuICAgIH1cbiAgfTtcblxuICAvLyBVcGRhdGUgc3RlcCBjb21wbGV0aW9uIHN0YXR1c1xuICBjb25zdCB1cGRhdGVTdGVwcyA9ICgpOiBGbG93U3RlcFtdID0+IHtcbiAgICBjb25zdCBzdGVwcyA9IGdldFN0ZXBzKCk7XG4gICAgcmV0dXJuIHN0ZXBzLm1hcCgoc3RlcCwgaW5kZXgpID0+ICh7XG4gICAgICAuLi5zdGVwLFxuICAgICAgY29tcGxldGVkOiBpbmRleCA8IGN1cnJlbnRTdGVwLFxuICAgICAgY3VycmVudDogaW5kZXggPT09IGN1cnJlbnRTdGVwXG4gICAgfSkpO1xuICB9O1xuXG4gIC8vIExvYWQgaW5pdGlhbCBkYXRhXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFNlcnZpY2VzKCk7XG4gICAgbG9hZExvY2F0aW9ucygpO1xuICB9LCBbb3JnYW5pemF0aW9uSWRdKTtcblxuICBjb25zdCBsb2FkU2VydmljZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3NlcnZpY2VzP29yZ2FuaXphdGlvbklkPSR7b3JnYW5pemF0aW9uSWR9YCk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0U2VydmljZXMoZGF0YS5zZXJ2aWNlcyB8fCBbXSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHNlcnZpY2VzOicsIGVycik7XG4gICAgICBzZXRFcnJvcignRXJyb3IgYWwgY2FyZ2FyIHNlcnZpY2lvcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZExvY2F0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9sb2NhdGlvbnM/b3JnYW5pemF0aW9uSWQ9JHtvcmdhbml6YXRpb25JZH1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRMb2NhdGlvbnMoZGF0YS5sb2NhdGlvbnMgfHwgW10pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBsb2NhdGlvbnM6JywgZXJyKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZERvY3RvcnMgPSBhc3luYyAoc2VydmljZUlkPzogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBsZXQgdXJsID0gYC9hcGkvZG9jdG9ycz9vcmdhbml6YXRpb25JZD0ke29yZ2FuaXphdGlvbklkfWA7XG4gICAgICBpZiAoc2VydmljZUlkKSB7XG4gICAgICAgIHVybCArPSBgJnNlcnZpY2VJZD0ke3NlcnZpY2VJZH1gO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgLy8gQ1JJVElDQUwgRklYOiBBUEkgcmV0dXJucyBkYXRhLmRhdGEsIG5vdCBkYXRhLmRvY3RvcnNcbiAgICAgICAgc2V0RG9jdG9ycyhkYXRhLmRhdGEgfHwgW10pO1xuICAgICAgICBjb25zb2xlLmxvZyhgREVCVUc6IExvYWRlZCAke2RhdGEuZGF0YT8ubGVuZ3RoIHx8IDB9IGRvY3RvcnMgZm9yIHNlcnZpY2UgJHtzZXJ2aWNlSWQgfHwgJ0FMTCd9YCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGRvY3RvcnM6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdFcnJvciBhbCBjYXJnYXIgZG9jdG9yZXMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvYWRBdmFpbGFiaWxpdHkgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFmb3JtRGF0YS5hcHBvaW50bWVudF9kYXRlKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGxldCB1cmwgPSBgL2FwaS9kb2N0b3JzL2F2YWlsYWJpbGl0eT9vcmdhbml6YXRpb25JZD0ke29yZ2FuaXphdGlvbklkfSZkYXRlPSR7Zm9ybURhdGEuYXBwb2ludG1lbnRfZGF0ZX1gO1xuXG4gICAgICBpZiAoZm9ybURhdGEuZG9jdG9yX2lkKSB7XG4gICAgICAgIHVybCArPSBgJmRvY3RvcklkPSR7Zm9ybURhdGEuZG9jdG9yX2lkfWA7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEuc2VydmljZV9pZCkge1xuICAgICAgICB1cmwgKz0gYCZzZXJ2aWNlSWQ9JHtmb3JtRGF0YS5zZXJ2aWNlX2lkfWA7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEubG9jYXRpb25faWQpIHtcbiAgICAgICAgdXJsICs9IGAmbG9jYXRpb25JZD0ke2Zvcm1EYXRhLmxvY2F0aW9uX2lkfWA7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRBdmFpbGFiaWxpdHkoZGF0YS5kYXRhIHx8IFtdKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgYXZhaWxhYmlsaXR5OicsIGVycik7XG4gICAgICBzZXRFcnJvcignRXJyb3IgYWwgY2FyZ2FyIGRpc3BvbmliaWxpZGFkJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBFbmhhbmNlZCBhdmFpbGFiaWxpdHkgbG9hZGVyIGZvciBXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvclxuICBjb25zdCBsb2FkV2Vla2x5QXZhaWxhYmlsaXR5ID0gYXN5bmMgKHBhcmFtczoge1xuICAgIG9yZ2FuaXphdGlvbklkOiBzdHJpbmc7XG4gICAgc2VydmljZUlkPzogc3RyaW5nO1xuICAgIGRvY3RvcklkPzogc3RyaW5nO1xuICAgIGxvY2F0aW9uSWQ/OiBzdHJpbmc7XG4gICAgc3RhcnREYXRlOiBzdHJpbmc7XG4gICAgZW5kRGF0ZTogc3RyaW5nO1xuICB9KTogUHJvbWlzZTxEYXlBdmFpbGFiaWxpdHlEYXRhW10+ID0+IHtcbiAgICB0cnkge1xuICAgICAgbGV0IHVybCA9IGAvYXBpL2FwcG9pbnRtZW50cy9hdmFpbGFiaWxpdHk/b3JnYW5pemF0aW9uSWQ9JHtwYXJhbXMub3JnYW5pemF0aW9uSWR9JnN0YXJ0RGF0ZT0ke3BhcmFtcy5zdGFydERhdGV9JmVuZERhdGU9JHtwYXJhbXMuZW5kRGF0ZX1gO1xuXG4gICAgICBpZiAocGFyYW1zLnNlcnZpY2VJZCkgdXJsICs9IGAmc2VydmljZUlkPSR7cGFyYW1zLnNlcnZpY2VJZH1gO1xuICAgICAgaWYgKHBhcmFtcy5kb2N0b3JJZCkgdXJsICs9IGAmZG9jdG9ySWQ9JHtwYXJhbXMuZG9jdG9ySWR9YDtcbiAgICAgIGlmIChwYXJhbXMubG9jYXRpb25JZCkgdXJsICs9IGAmbG9jYXRpb25JZD0ke3BhcmFtcy5sb2NhdGlvbklkfWA7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvciAke3Jlc3BvbnNlLnN0YXR1c306ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdFcnJvciBkZXNjb25vY2lkbyBhbCBjYXJnYXIgZGlzcG9uaWJpbGlkYWQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gUHJvY2VzcyBBUEkgcmVzcG9uc2UgaW50byBEYXlBdmFpbGFiaWxpdHlEYXRhIGZvcm1hdFxuICAgICAgY29uc3QgZGF5TmFtZXMgPSBbJ0RvbWluZ28nLCAnTHVuZXMnLCAnTWFydGVzJywgJ01pw6lyY29sZXMnLCAnSnVldmVzJywgJ1ZpZXJuZXMnLCAnU8OhYmFkbyddO1xuICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpO1xuICAgICAgY29uc3QgdG9tb3Jyb3cgPSBuZXcgRGF0ZSh0b2RheSk7XG4gICAgICB0b21vcnJvdy5zZXREYXRlKHRvZGF5LmdldERhdGUoKSArIDEpO1xuXG4gICAgICBjb25zdCBwcm9jZXNzZWREYXRhOiBEYXlBdmFpbGFiaWxpdHlEYXRhW10gPSBbXTtcbiAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHBhcmFtcy5zdGFydERhdGUpO1xuICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHBhcmFtcy5lbmREYXRlKTtcblxuICAgICAgZm9yIChsZXQgZGF0ZSA9IG5ldyBEYXRlKHN0YXJ0RGF0ZSk7IGRhdGUgPD0gZW5kRGF0ZTsgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpICsgMSkpIHtcbiAgICAgICAgY29uc3QgZGF0ZVN0cmluZyA9IGRhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgICBjb25zdCBkYXlEYXRhID0gcmVzdWx0LmRhdGFbZGF0ZVN0cmluZ107XG5cbiAgICAgICAgY29uc3QgYXZhaWxhYmxlU2xvdHMgPSBkYXlEYXRhPy5hdmFpbGFibGVTbG90cyB8fCAwO1xuICAgICAgICBjb25zdCBpc1RvZGF5ID0gZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0gdG9kYXkudG9EYXRlU3RyaW5nKCk7XG4gICAgICAgIGNvbnN0IGlzVG9tb3Jyb3cgPSBkYXRlLnRvRGF0ZVN0cmluZygpID09PSB0b21vcnJvdy50b0RhdGVTdHJpbmcoKTtcbiAgICAgICAgY29uc3QgaXNXZWVrZW5kID0gZGF0ZS5nZXREYXkoKSA9PT0gMCB8fCBkYXRlLmdldERheSgpID09PSA2O1xuXG4gICAgICAgIGxldCBhdmFpbGFiaWxpdHlMZXZlbDogJ2hpZ2gnIHwgJ21lZGl1bScgfCAnbG93JyB8ICdub25lJyA9ICdub25lJztcbiAgICAgICAgaWYgKGF2YWlsYWJsZVNsb3RzID09PSAwKSBhdmFpbGFiaWxpdHlMZXZlbCA9ICdub25lJztcbiAgICAgICAgZWxzZSBpZiAoYXZhaWxhYmxlU2xvdHMgPD0gMikgYXZhaWxhYmlsaXR5TGV2ZWwgPSAnbG93JztcbiAgICAgICAgZWxzZSBpZiAoYXZhaWxhYmxlU2xvdHMgPD0gNSkgYXZhaWxhYmlsaXR5TGV2ZWwgPSAnbWVkaXVtJztcbiAgICAgICAgZWxzZSBhdmFpbGFiaWxpdHlMZXZlbCA9ICdoaWdoJztcblxuICAgICAgICBwcm9jZXNzZWREYXRhLnB1c2goe1xuICAgICAgICAgIGRhdGU6IGRhdGVTdHJpbmcsXG4gICAgICAgICAgZGF5TmFtZTogZGF5TmFtZXNbZGF0ZS5nZXREYXkoKV0sXG4gICAgICAgICAgc2xvdHNDb3VudDogYXZhaWxhYmxlU2xvdHMsXG4gICAgICAgICAgYXZhaWxhYmlsaXR5TGV2ZWwsXG4gICAgICAgICAgaXNUb2RheSxcbiAgICAgICAgICBpc1RvbW9ycm93LFxuICAgICAgICAgIGlzV2Vla2VuZCxcbiAgICAgICAgICBzbG90czogZGF5RGF0YT8uc2xvdHMgfHwgW11cbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBwcm9jZXNzZWREYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHdlZWtseSBhdmFpbGFiaWxpdHk6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIC8vIE5hdmlnYXRpb24gaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlTmV4dCA9ICgpID0+IHtcbiAgICBjb25zdCBzdGVwcyA9IGdldFN0ZXBzKCk7XG4gICAgaWYgKGN1cnJlbnRTdGVwIDwgc3RlcHMubGVuZ3RoIC0gMSkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgKyAxKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVCYWNrID0gKCkgPT4ge1xuICAgIGlmIChjdXJyZW50U3RlcCA+IDApIHtcbiAgICAgIGNvbnN0IG5ld1N0ZXAgPSBjdXJyZW50U3RlcCAtIDE7XG4gICAgICBzZXRDdXJyZW50U3RlcChuZXdTdGVwKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBDUklUSUNBTCBGSVg6IFJlc2V0IGJvb2tpbmdGbG93IHdoZW4gcmV0dXJuaW5nIHRvIGZsb3cgc2VsZWN0aW9uIHN0ZXBcbiAgICAgIGNvbnN0IHN0ZXBzID0gZ2V0U3RlcHMoKTtcbiAgICAgIGNvbnN0IGZsb3dTdGVwSW5kZXggPSBzdGVwcy5maW5kSW5kZXgoc3RlcCA9PiBzdGVwLmlkID09PSAnZmxvdycpO1xuICAgICAgaWYgKG5ld1N0ZXAgPT09IGZsb3dTdGVwSW5kZXgpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0RFQlVHOiBSZXNldHRpbmcgYm9va2luZ0Zsb3cgc3RhdGUgZm9yIGZsb3cgc2VsZWN0aW9uIHN0ZXAnKTtcbiAgICAgICAgc2V0Qm9va2luZ0Zsb3cobnVsbCk7XG4gICAgICAgIHNldE9wdGltYWxBcHBvaW50bWVudChudWxsKTtcbiAgICAgICAgc2V0SXNTZWFyY2hpbmdPcHRpbWFsKGZhbHNlKTtcbiAgICAgICAgLy8gQ2xlYXIgZG9jdG9ycyB0byBmb3JjZSByZWxvYWQgd2hlbiBmbG93IGlzIHNlbGVjdGVkIGFnYWluXG4gICAgICAgIHNldERvY3RvcnMoW10pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDYW5jZWwgPSAoKSA9PiB7XG4gICAgc2V0U2hvd0NhbmNlbERpYWxvZyh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDb25maXJtQ2FuY2VsID0gKCkgPT4ge1xuICAgIHNldFNob3dDYW5jZWxEaWFsb2coZmFsc2UpO1xuICAgIG9uQ2FuY2VsPy4oKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDYW5jZWxEaWFsb2cgPSAoKSA9PiB7XG4gICAgc2V0U2hvd0NhbmNlbERpYWxvZyhmYWxzZSk7XG4gIH07XG5cbiAgLy8gU2VsZWN0aW9uIGhhbmRsZXJzXG4gIGNvbnN0IGhhbmRsZVNlcnZpY2VTZWxlY3QgPSAob3B0aW9uOiBTZWxlY3Rpb25PcHRpb24pID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHNlcnZpY2VfaWQ6IG9wdGlvbi5pZCB9KSk7XG4gICAgLy8gRG9uJ3QgbG9hZCBkb2N0b3JzIHlldCAtIHdhaXQgZm9yIGZsb3cgc2VsZWN0aW9uXG4gICAgaGFuZGxlTmV4dCgpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURvY3RvclNlbGVjdCA9IChvcHRpb246IFNlbGVjdGlvbk9wdGlvbikgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgZG9jdG9yX2lkOiBvcHRpb24uaWQgfSkpO1xuICAgIGhhbmRsZU5leHQoKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVMb2NhdGlvblNlbGVjdCA9IChvcHRpb246IFNlbGVjdGlvbk9wdGlvbikgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgbG9jYXRpb25faWQ6IG9wdGlvbi5pZCB9KSk7XG4gICAgaGFuZGxlTmV4dCgpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURhdGVTZWxlY3QgPSAoZGF0ZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBhcHBvaW50bWVudF9kYXRlOiBkYXRlLCBhcHBvaW50bWVudF90aW1lOiAnJyB9KSk7XG4gICAgc2V0U2VsZWN0ZWRTbG90KG51bGwpO1xuICAgIGhhbmRsZU5leHQoKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTbG90U2VsZWN0ID0gKHNsb3Q6IEF2YWlsYWJpbGl0eVNsb3QpID0+IHtcbiAgICBzZXRTZWxlY3RlZFNsb3Qoc2xvdCk7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGFwcG9pbnRtZW50X3RpbWU6IHNsb3Quc3RhcnRfdGltZSxcbiAgICAgIGRvY3Rvcl9pZDogc2xvdC5kb2N0b3JfaWQgLy8gQXV0by1hc3NpZ24gZG9jdG9yIGlmIG5vdCBzZWxlY3RlZFxuICAgIH0pKTtcbiAgICBoYW5kbGVOZXh0KCk7XG4gIH07XG5cbiAgLy8gSHlicmlkIGZsb3cgaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlRmxvd1NlbGVjdCA9IGFzeW5jIChmbG93VHlwZTogQm9va2luZ0Zsb3dUeXBlKSA9PiB7XG4gICAgc2V0Qm9va2luZ0Zsb3coZmxvd1R5cGUpO1xuXG4gICAgaWYgKGZsb3dUeXBlID09PSAnZXhwcmVzcycpIHtcbiAgICAgIC8vIFNob3cgc2VhcmNoaW5nIHN0YXRlIGFuZCBmaW5kIG9wdGltYWwgYXBwb2ludG1lbnRcbiAgICAgIHNldElzU2VhcmNoaW5nT3B0aW1hbCh0cnVlKTtcbiAgICAgIGhhbmRsZU5leHQoKTsgLy8gTW92ZSB0byBzZWFyY2hpbmcgc3RlcFxuICAgICAgYXdhaXQgZmluZE9wdGltYWxBcHBvaW50bWVudCgpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBDb250aW51ZSB3aXRoIHBlcnNvbmFsaXplZCBmbG93XG4gICAgICBsb2FkRG9jdG9ycyhmb3JtRGF0YS5zZXJ2aWNlX2lkKTtcbiAgICAgIGhhbmRsZU5leHQoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZmluZE9wdGltYWxBcHBvaW50bWVudCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWZvcm1EYXRhLnNlcnZpY2VfaWQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgIC8vIEFkZCBtaW5pbXVtIGRlbGF5IHRvIHNob3cgc2VhcmNoaW5nIGFuaW1hdGlvblxuICAgICAgY29uc3Qgc2VhcmNoUHJvbWlzZSA9IG5ldyBPcHRpbWFsQXBwb2ludG1lbnRGaW5kZXIoKS5maW5kT3B0aW1hbEFwcG9pbnRtZW50KHtcbiAgICAgICAgc2VydmljZUlkOiBmb3JtRGF0YS5zZXJ2aWNlX2lkLFxuICAgICAgICBvcmdhbml6YXRpb25JZCxcbiAgICAgICAgcHJlZmVyZW5jZXM6IHtcbiAgICAgICAgICBtYXhEYXlzT3V0OiAxNCxcbiAgICAgICAgICB0aW1lUHJlZmVyZW5jZTogJ2FueSdcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIC8vIEVuc3VyZSBtaW5pbXVtIDggc2Vjb25kcyBmb3IgVVggKG1hdGNoZXMgRXhwcmVzc1NlYXJjaGluZ1N0YXRlIGFuaW1hdGlvbilcbiAgICAgIGNvbnN0IFtyZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBzZWFyY2hQcm9taXNlLFxuICAgICAgICBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgODAwMCkpXG4gICAgICBdKTtcblxuICAgICAgaWYgKHJlc3VsdCkge1xuICAgICAgICBzZXRPcHRpbWFsQXBwb2ludG1lbnQocmVzdWx0KTtcbiAgICAgICAgLy8gVXBkYXRlIGZvcm0gZGF0YSB3aXRoIG9wdGltYWwgYXBwb2ludG1lbnRcbiAgICAgICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgZG9jdG9yX2lkOiByZXN1bHQuYXBwb2ludG1lbnQuZG9jdG9ySWQsXG4gICAgICAgICAgbG9jYXRpb25faWQ6IHJlc3VsdC5hcHBvaW50bWVudC5sb2NhdGlvbklkLFxuICAgICAgICAgIGFwcG9pbnRtZW50X2RhdGU6IHJlc3VsdC5hcHBvaW50bWVudC5kYXRlLFxuICAgICAgICAgIGFwcG9pbnRtZW50X3RpbWU6IHJlc3VsdC5hcHBvaW50bWVudC5zdGFydFRpbWVcbiAgICAgICAgfSkpO1xuICAgICAgICBzZXRJc1NlYXJjaGluZ09wdGltYWwoZmFsc2UpO1xuICAgICAgICAvLyBTa2lwIHRvIGNvbmZpcm1hdGlvbiBzdGVwIChzdGVwIDIgaW4gZXhwcmVzcyBmbG93KVxuICAgICAgICBzZXRDdXJyZW50U3RlcCgyKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldElzU2VhcmNoaW5nT3B0aW1hbChmYWxzZSk7XG4gICAgICAgIHNldEVycm9yKCdObyBzZSBlbmNvbnRyYXJvbiBjaXRhcyBkaXNwb25pYmxlcy4gSW50ZW50YSBjb24gbGEgcmVzZXJ2YSBwZXJzb25hbGl6YWRhLicpO1xuICAgICAgICBzZXRCb29raW5nRmxvdygncGVyc29uYWxpemVkJyk7XG4gICAgICAgIGxvYWREb2N0b3JzKGZvcm1EYXRhLnNlcnZpY2VfaWQpO1xuICAgICAgICBzZXRDdXJyZW50U3RlcCgyKTsgLy8gR28gdG8gZG9jdG9yIHNlbGVjdGlvbiBpbiBwZXJzb25hbGl6ZWQgZmxvd1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmluZGluZyBvcHRpbWFsIGFwcG9pbnRtZW50OicsIGVycik7XG4gICAgICBzZXRJc1NlYXJjaGluZ09wdGltYWwoZmFsc2UpO1xuICAgICAgc2V0RXJyb3IoJ0Vycm9yIGFsIGJ1c2NhciBjaXRhcyBkaXNwb25pYmxlcy4gSW50ZW50YSBjb24gbGEgcmVzZXJ2YSBwZXJzb25hbGl6YWRhLicpO1xuICAgICAgc2V0Qm9va2luZ0Zsb3coJ3BlcnNvbmFsaXplZCcpO1xuICAgICAgbG9hZERvY3RvcnMoZm9ybURhdGEuc2VydmljZV9pZCk7XG4gICAgICBzZXRDdXJyZW50U3RlcCgyKTsgLy8gR28gdG8gZG9jdG9yIHNlbGVjdGlvbiBpbiBwZXJzb25hbGl6ZWQgZmxvd1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRXhwcmVzc0NvbmZpcm0gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFvcHRpbWFsQXBwb2ludG1lbnQgfHwgIXVzZXJJZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgY29uc3QgYm9va2luZ0RhdGEgPSB7XG4gICAgICAgIG9yZ2FuaXphdGlvbklkLFxuICAgICAgICBwYXRpZW50SWQ6IHVzZXJJZCxcbiAgICAgICAgZG9jdG9ySWQ6IG9wdGltYWxBcHBvaW50bWVudC5hcHBvaW50bWVudC5kb2N0b3JJZCxcbiAgICAgICAgc2VydmljZUlkOiBmb3JtRGF0YS5zZXJ2aWNlX2lkLFxuICAgICAgICBsb2NhdGlvbklkOiBvcHRpbWFsQXBwb2ludG1lbnQuYXBwb2ludG1lbnQubG9jYXRpb25JZCxcbiAgICAgICAgYXBwb2ludG1lbnREYXRlOiBvcHRpbWFsQXBwb2ludG1lbnQuYXBwb2ludG1lbnQuZGF0ZSxcbiAgICAgICAgc3RhcnRUaW1lOiBvcHRpbWFsQXBwb2ludG1lbnQuYXBwb2ludG1lbnQuc3RhcnRUaW1lLFxuICAgICAgICBlbmRUaW1lOiBvcHRpbWFsQXBwb2ludG1lbnQuYXBwb2ludG1lbnQuZW5kVGltZSxcbiAgICAgICAgcmVhc29uOiBmb3JtRGF0YS5yZWFzb24sXG4gICAgICAgIG5vdGVzOiBmb3JtRGF0YS5ub3RlcyB8fCBgQ2l0YSBhZ2VuZGFkYSB2aWEgJHttb2RlID09PSAnYWknID8gJ0FJIEFzc2lzdGFudCcgOiAnUmVzZXJ2YSBFeHByZXNzJ31gXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FwcG9pbnRtZW50cycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShib29raW5nRGF0YSksXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MgfHwgcmVzcG9uc2Uub2spIHtcbiAgICAgICAgb25BcHBvaW50bWVudEJvb2tlZD8uKHJlc3VsdC5hcHBvaW50bWVudElkIHx8IHJlc3VsdC5kYXRhPy5pZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gY3JlYXRlIGFwcG9pbnRtZW50Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBib29raW5nIGV4cHJlc3MgYXBwb2ludG1lbnQ6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRXJyb3IgYWwgYWdlbmRhciBsYSBjaXRhJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDdXN0b21pemVGcm9tRXhwcmVzcyA9ICgpID0+IHtcbiAgICBzZXRCb29raW5nRmxvdygncGVyc29uYWxpemVkJyk7XG4gICAgc2V0T3B0aW1hbEFwcG9pbnRtZW50KG51bGwpO1xuICAgIHNldElzU2VhcmNoaW5nT3B0aW1hbChmYWxzZSk7XG4gICAgbG9hZERvY3RvcnMoZm9ybURhdGEuc2VydmljZV9pZCk7XG4gICAgc2V0Q3VycmVudFN0ZXAoMik7IC8vIEdvIGRpcmVjdGx5IHRvIGRvY3RvciBzZWxlY3Rpb24gaW4gcGVyc29uYWxpemVkIGZsb3dcbiAgfTtcblxuICBjb25zdCBoYW5kbGVCYWNrVG9GbG93U2VsZWN0aW9uID0gKCkgPT4ge1xuICAgIHNldEJvb2tpbmdGbG93KG51bGwpO1xuICAgIHNldE9wdGltYWxBcHBvaW50bWVudChudWxsKTtcbiAgICBzZXRJc1NlYXJjaGluZ09wdGltYWwoZmFsc2UpO1xuICAgIHNldEN1cnJlbnRTdGVwKDEpOyAvLyBHbyBiYWNrIHRvIGZsb3cgc2VsZWN0aW9uXG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2FuY2VsRXhwcmVzc1NlYXJjaCA9ICgpID0+IHtcbiAgICBzZXRJc1NlYXJjaGluZ09wdGltYWwoZmFsc2UpO1xuICAgIHNldEJvb2tpbmdGbG93KCdwZXJzb25hbGl6ZWQnKTtcbiAgICBsb2FkRG9jdG9ycyhmb3JtRGF0YS5zZXJ2aWNlX2lkKTtcbiAgICBzZXRDdXJyZW50U3RlcCgyKTsgLy8gR28gdG8gZG9jdG9yIHNlbGVjdGlvbiBpbiBwZXJzb25hbGl6ZWQgZmxvd1xuICB9O1xuXG4gIC8vIExvYWQgYXZhaWxhYmlsaXR5IHdoZW4gZGF0ZSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc3RlcHMgPSBnZXRTdGVwcygpO1xuICAgIGNvbnN0IHRpbWVTdGVwSW5kZXggPSBzdGVwcy5maW5kSW5kZXgoc3RlcCA9PiBzdGVwLmlkID09PSAndGltZScpO1xuICAgIGlmIChmb3JtRGF0YS5hcHBvaW50bWVudF9kYXRlICYmIGN1cnJlbnRTdGVwID09PSB0aW1lU3RlcEluZGV4KSB7XG4gICAgICBsb2FkQXZhaWxhYmlsaXR5KCk7XG4gICAgfVxuICB9LCBbZm9ybURhdGEuYXBwb2ludG1lbnRfZGF0ZSwgY3VycmVudFN0ZXAsIGJvb2tpbmdGbG93XSk7XG5cbiAgLy8gQ1JJVElDQUwgRklYOiBWYWxpZGF0ZSBzdGF0ZSBjb25zaXN0ZW5jeSB0byBwcmV2ZW50IG5hdmlnYXRpb24gYnVnc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHN0ZXBzID0gZ2V0U3RlcHMoKTtcbiAgICBjb25zdCBmbG93U3RlcEluZGV4ID0gc3RlcHMuZmluZEluZGV4KHN0ZXAgPT4gc3RlcC5pZCA9PT0gJ2Zsb3cnKTtcblxuICAgIC8vIElmIHdlJ3JlIG9uIGZsb3cgc2VsZWN0aW9uIHN0ZXAgYnV0IGJvb2tpbmdGbG93IGlzIGFscmVhZHkgc2V0LCByZXNldCBpdFxuICAgIGlmIChjdXJyZW50U3RlcCA9PT0gZmxvd1N0ZXBJbmRleCAmJiBib29raW5nRmxvdykge1xuICAgICAgY29uc29sZS53YXJuKCdERUJVRzogSW5jb25zaXN0ZW50IHN0YXRlIGRldGVjdGVkIC0gcmVzZXR0aW5nIGJvb2tpbmdGbG93IG9uIGZsb3cgc3RlcCcpO1xuICAgICAgc2V0Qm9va2luZ0Zsb3cobnVsbCk7XG4gICAgICBzZXRPcHRpbWFsQXBwb2ludG1lbnQobnVsbCk7XG4gICAgICBzZXRJc1NlYXJjaGluZ09wdGltYWwoZmFsc2UpO1xuICAgIH1cbiAgfSwgW2N1cnJlbnRTdGVwLCBib29raW5nRmxvd10pO1xuXG4gIC8vIEhhbmRsZSBhcHBvaW50bWVudCBib29raW5nXG4gIGNvbnN0IGhhbmRsZUJvb2tBcHBvaW50bWVudCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkU2xvdCB8fCAhdXNlcklkKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICBjb25zdCBib29raW5nRGF0YSA9IHtcbiAgICAgICAgb3JnYW5pemF0aW9uSWQsXG4gICAgICAgIHBhdGllbnRJZDogdXNlcklkLFxuICAgICAgICBkb2N0b3JJZDogc2VsZWN0ZWRTbG90LmRvY3Rvcl9pZCxcbiAgICAgICAgc2VydmljZUlkOiBmb3JtRGF0YS5zZXJ2aWNlX2lkLFxuICAgICAgICBsb2NhdGlvbklkOiBmb3JtRGF0YS5sb2NhdGlvbl9pZCxcbiAgICAgICAgYXBwb2ludG1lbnREYXRlOiBmb3JtRGF0YS5hcHBvaW50bWVudF9kYXRlLFxuICAgICAgICBzdGFydFRpbWU6IHNlbGVjdGVkU2xvdC5zdGFydF90aW1lLFxuICAgICAgICBlbmRUaW1lOiBzZWxlY3RlZFNsb3QuZW5kX3RpbWUsXG4gICAgICAgIHJlYXNvbjogZm9ybURhdGEucmVhc29uLFxuICAgICAgICBub3RlczogZm9ybURhdGEubm90ZXMgfHwgYENpdGEgYWdlbmRhZGEgdmlhICR7bW9kZSA9PT0gJ2FpJyA/ICdBSSBBc3Npc3RhbnQnIDogJ2Zvcm11bGFyaW8gbWFudWFsJ31gXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FwcG9pbnRtZW50cycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShib29raW5nRGF0YSksXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MgfHwgcmVzcG9uc2Uub2spIHtcbiAgICAgICAgb25BcHBvaW50bWVudEJvb2tlZD8uKHJlc3VsdC5hcHBvaW50bWVudElkIHx8IHJlc3VsdC5kYXRhPy5pZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gY3JlYXRlIGFwcG9pbnRtZW50Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBib29raW5nIGFwcG9pbnRtZW50OicsIGVycik7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0Vycm9yIGFsIGFnZW5kYXIgbGEgY2l0YScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICB7LyogUHJvZ3Jlc3MgSW5kaWNhdG9yICovfVxuICAgICAgICA8UHJvZ3Jlc3NJbmRpY2F0b3JcbiAgICAgICAgICBzdGVwcz17dXBkYXRlU3RlcHMoKX1cbiAgICAgICAgICBjdXJyZW50U3RlcD17Y3VycmVudFN0ZXB9XG4gICAgICAgIC8+XG5cbiAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICA8QWxlcnRNZXNzYWdlXG4gICAgICAgICAgICB0eXBlPVwiZXJyb3JcIlxuICAgICAgICAgICAgdGl0bGU9XCJFcnJvclwiXG4gICAgICAgICAgICBtZXNzYWdlPXtlcnJvcn1cbiAgICAgICAgICAgIG9uRGlzbWlzcz17KCkgPT4gc2V0RXJyb3IobnVsbCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtYi02XCJcbiAgICAgICAgICAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBTdGVwIDA6IFNlcnZpY2UgU2VsZWN0aW9uICovfVxuICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDAgJiYgKFxuICAgICAgICAgIDxTZWxlY3Rpb25DYXJkXG4gICAgICAgICAgICB0aXRsZT1cIsK/UXXDqSB0aXBvIGRlIGNvbnN1bHRhIG5lY2VzaXRhcz9cIlxuICAgICAgICAgICAgc3VidGl0bGU9XCJTZWxlY2Npb25hIGVsIHNlcnZpY2lvIG3DqWRpY28gcXVlIHJlcXVpZXJlc1wiXG4gICAgICAgICAgICBvcHRpb25zPXtzZXJ2aWNlcy5tYXAoc2VydmljZSA9PiAoe1xuICAgICAgICAgICAgICBpZDogc2VydmljZS5pZCxcbiAgICAgICAgICAgICAgdGl0bGU6IHNlcnZpY2UubmFtZSxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHNlcnZpY2UuZGVzY3JpcHRpb24sXG4gICAgICAgICAgICAgIHByaWNlOiBzZXJ2aWNlLnByaWNlXG4gICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICBzZWxlY3RlZElkPXtmb3JtRGF0YS5zZXJ2aWNlX2lkfVxuICAgICAgICAgICAgb25TZWxlY3Q9e2hhbmRsZVNlcnZpY2VTZWxlY3R9XG4gICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgICAgZW1wdHlNZXNzYWdlPVwiTm8gaGF5IHNlcnZpY2lvcyBkaXNwb25pYmxlc1wiXG4gICAgICAgICAgICBncmlkQ29scz17MX1cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBTdGVwIDE6IEZsb3cgU2VsZWN0aW9uICovfVxuICAgICAgICB7KCgpID0+IHtcbiAgICAgICAgICBjb25zdCBzdGVwcyA9IGdldFN0ZXBzKCk7XG4gICAgICAgICAgY29uc3QgZmxvd1N0ZXBJbmRleCA9IHN0ZXBzLmZpbmRJbmRleChzdGVwID0+IHN0ZXAuaWQgPT09ICdmbG93Jyk7XG4gICAgICAgICAgcmV0dXJuIGN1cnJlbnRTdGVwID09PSBmbG93U3RlcEluZGV4ICYmICFib29raW5nRmxvdyAmJiAoXG4gICAgICAgICAgICA8Rmxvd1NlbGVjdG9yXG4gICAgICAgICAgICAgIG9uRmxvd1NlbGVjdD17aGFuZGxlRmxvd1NlbGVjdH1cbiAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSkoKX1cblxuICAgICAgICB7LyogRXhwcmVzcyBTZWFyY2hpbmcgU3RhdGUgKi99XG4gICAgICAgIHsoKCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHN0ZXBzID0gZ2V0U3RlcHMoKTtcbiAgICAgICAgICBjb25zdCBmbG93U3RlcEluZGV4ID0gc3RlcHMuZmluZEluZGV4KHN0ZXAgPT4gc3RlcC5pZCA9PT0gJ2Zsb3cnKTtcbiAgICAgICAgICByZXR1cm4gY3VycmVudFN0ZXAgPT09IGZsb3dTdGVwSW5kZXggKyAxICYmIGJvb2tpbmdGbG93ID09PSAnZXhwcmVzcycgJiYgaXNTZWFyY2hpbmdPcHRpbWFsICYmIChcbiAgICAgICAgICAgIDxFeHByZXNzU2VhcmNoaW5nU3RhdGVcbiAgICAgICAgICAgICAgb25DYW5jZWw9e2hhbmRsZUNhbmNlbEV4cHJlc3NTZWFyY2h9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICk7XG4gICAgICAgIH0pKCl9XG5cbiAgICAgICAgey8qIERvY3RvciBTZWxlY3Rpb24gKFBlcnNvbmFsaXplZCBGbG93KSAqL31cbiAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RlcHMgPSBnZXRTdGVwcygpO1xuICAgICAgICAgIGNvbnN0IGRvY3RvclN0ZXBJbmRleCA9IHN0ZXBzLmZpbmRJbmRleChzdGVwID0+IHN0ZXAuaWQgPT09ICdkb2N0b3InKTtcbiAgICAgICAgICByZXR1cm4gY3VycmVudFN0ZXAgPT09IGRvY3RvclN0ZXBJbmRleCAmJiBib29raW5nRmxvdyA9PT0gJ3BlcnNvbmFsaXplZCcgJiYgKFxuICAgICAgICAgICAgPFNlbGVjdGlvbkNhcmRcbiAgICAgICAgICAgICAgdGl0bGU9XCLCv1RpZW5lcyBwcmVmZXJlbmNpYSBwb3IgYWxnw7puIGRvY3Rvcj9cIlxuICAgICAgICAgICAgICBzdWJ0aXRsZT1cIlB1ZWRlcyBlbGVnaXIgdW4gZG9jdG9yIGVzcGVjw61maWNvIG8gY29udGludWFyIHBhcmEgdmVyIGRpc3BvbmliaWxpZGFkIGdlbmVyYWxcIlxuICAgICAgICAgICAgICBvcHRpb25zPXtbXG4gICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgaWQ6ICcnLFxuICAgICAgICAgICAgICAgICAgdGl0bGU6ICdDdWFscXVpZXIgZG9jdG9yIGRpc3BvbmlibGUnLFxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGBWZXIgZGlzcG9uaWJpbGlkYWQgZGUgdG9kb3MgbG9zIGRvY3RvcmVzICgke2RvY3RvcnMubGVuZ3RofSBkaXNwb25pYmxlcylgLFxuICAgICAgICAgICAgICAgICAgc3VidGl0bGU6ICdSZWNvbWVuZGFkbyBwYXJhIG1heW9yIGZsZXhpYmlsaWRhZCBkZSBob3JhcmlvcydcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIC4uLmRvY3RvcnMubWFwKGRvY3RvciA9PiAoe1xuICAgICAgICAgICAgICAgICAgaWQ6IGRvY3Rvci5pZCxcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiBgRHIuICR7ZG9jdG9yLnByb2ZpbGVzLmZpcnN0X25hbWV9ICR7ZG9jdG9yLnByb2ZpbGVzLmxhc3RfbmFtZX1gLFxuICAgICAgICAgICAgICAgICAgc3VidGl0bGU6IGRvY3Rvci5zcGVjaWFsaXphdGlvbixcbiAgICAgICAgICAgICAgICAgIHByaWNlOiBkb2N0b3IuY29uc3VsdGF0aW9uX2ZlZVxuICAgICAgICAgICAgICAgIH0pKVxuICAgICAgICAgICAgICBdfVxuICAgICAgICAgICAgICBzZWxlY3RlZElkPXtmb3JtRGF0YS5kb2N0b3JfaWR9XG4gICAgICAgICAgICAgIG9uU2VsZWN0PXtoYW5kbGVEb2N0b3JTZWxlY3R9XG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGVtcHR5TWVzc2FnZT1cIk5vIGhheSBkb2N0b3JlcyBkaXNwb25pYmxlc1wiXG4gICAgICAgICAgICAgIGdyaWRDb2xzPXsxfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApO1xuICAgICAgICB9KSgpfVxuXG4gICAgICAgIHsvKiBMb2NhdGlvbiBTZWxlY3Rpb24gKFBlcnNvbmFsaXplZCBGbG93KSAqL31cbiAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RlcHMgPSBnZXRTdGVwcygpO1xuICAgICAgICAgIGNvbnN0IGxvY2F0aW9uU3RlcEluZGV4ID0gc3RlcHMuZmluZEluZGV4KHN0ZXAgPT4gc3RlcC5pZCA9PT0gJ2xvY2F0aW9uJyk7XG4gICAgICAgICAgcmV0dXJuIGN1cnJlbnRTdGVwID09PSBsb2NhdGlvblN0ZXBJbmRleCAmJiBib29raW5nRmxvdyA9PT0gJ3BlcnNvbmFsaXplZCcgJiYgKFxuICAgICAgICAgICAgPFNlbGVjdGlvbkNhcmRcbiAgICAgICAgICAgICAgdGl0bGU9XCLCv0VuIHF1w6kgc2VkZSBwcmVmaWVyZXMgbGEgY29uc3VsdGE/XCJcbiAgICAgICAgICAgICAgc3VidGl0bGU9XCJTZWxlY2Npb25hIGxhIHViaWNhY2nDs24gbcOhcyBjb252ZW5pZW50ZSBwYXJhIHRpXCJcbiAgICAgICAgICAgICAgb3B0aW9ucz17W1xuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgIGlkOiAnJyxcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiAnQ3VhbHF1aWVyIHNlZGUgZGlzcG9uaWJsZScsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYFZlciBkaXNwb25pYmlsaWRhZCBlbiB0b2RhcyBsYXMgc2VkZXMgKCR7bG9jYXRpb25zLmxlbmd0aH0gZGlzcG9uaWJsZXMpYCxcbiAgICAgICAgICAgICAgICAgIHN1YnRpdGxlOiAnUmVjb21lbmRhZG8gcGFyYSBtYXlvciBmbGV4aWJpbGlkYWQgZGUgaG9yYXJpb3MnXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAuLi5sb2NhdGlvbnMubWFwKGxvY2F0aW9uID0+ICh7XG4gICAgICAgICAgICAgICAgICBpZDogbG9jYXRpb24uaWQsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogbG9jYXRpb24ubmFtZSxcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBsb2NhdGlvbi5hZGRyZXNzXG4gICAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICAgIF19XG4gICAgICAgICAgICAgIHNlbGVjdGVkSWQ9e2Zvcm1EYXRhLmxvY2F0aW9uX2lkfVxuICAgICAgICAgICAgICBvblNlbGVjdD17aGFuZGxlTG9jYXRpb25TZWxlY3R9XG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGVtcHR5TWVzc2FnZT1cIk5vIGhheSBzZWRlcyBkaXNwb25pYmxlc1wiXG4gICAgICAgICAgICAgIGdyaWRDb2xzPXsxfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApO1xuICAgICAgICB9KSgpfVxuXG4gICAgICAgIHsvKiBEYXRlIFNlbGVjdGlvbiAoUGVyc29uYWxpemVkIEZsb3cpIC0gRW5oYW5jZWQgd2l0aCBXZWVrbHkgVmlldyAqL31cbiAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RlcHMgPSBnZXRTdGVwcygpO1xuICAgICAgICAgIGNvbnN0IGRhdGVTdGVwSW5kZXggPSBzdGVwcy5maW5kSW5kZXgoc3RlcCA9PiBzdGVwLmlkID09PSAnZGF0ZScpO1xuXG4gICAgICAgICAgLy8gQ1JJVElDQUwgRklYOiBEZXRlY3RhciBtb2RvIGRlIGVkaWNpw7NuIHBhcmEgcGVybWl0aXIgbmF2ZWdhY2nDs24gc2VtYW5hbCBmbGV4aWJsZVxuICAgICAgICAgIGNvbnN0IGlzRWRpdE1vZGUgPSBjdXJyZW50U3RlcCA8IHN0ZXBzLmxlbmd0aCAtIDEgJiYgZm9ybURhdGEuYXBwb2ludG1lbnRfZGF0ZTtcblxuICAgICAgICAgIC8qKlxuICAgICAgICAgICAqIENhbGN1bGFyIGZlY2hhIG3DrW5pbWEgYXByb3BpYWRhIHNlZ8O6biBlbCBjb250ZXh0b1xuICAgICAgICAgICAqIC0gTW9kbyBpbmljaWFsOiBTb2xvIGZlY2hhcyBmdXR1cmFzIChob3kgZW4gYWRlbGFudGUpXG4gICAgICAgICAgICogLSBNb2RvIGVkaWNpw7NuOiBQZXJtaXRpciBmZWNoYSB5YSBzZWxlY2Npb25hZGEgbyBmZWNoYXMgZnV0dXJhc1xuICAgICAgICAgICAqIE5PVEE6IEVzdGEgbMOzZ2ljYSB0YW1iacOpbiBhZmVjdGEgbGEgbmF2ZWdhY2nDs24gc2VtYW5hbCBlbiBXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvclxuICAgICAgICAgICAqL1xuICAgICAgICAgIGNvbnN0IGdldE1pbkRhdGUgPSAoKSA9PiB7XG4gICAgICAgICAgICBpZiAoaXNFZGl0TW9kZSkge1xuICAgICAgICAgICAgICAvLyBFbiBtb2RvIGVkaWNpw7NuLCBwZXJtaXRpciBsYSBmZWNoYSB5YSBzZWxlY2Npb25hZGEgc2kgZXMgYW50ZXJpb3IgYSBob3lcbiAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWREYXRlID0gZm9ybURhdGEuYXBwb2ludG1lbnRfZGF0ZTtcbiAgICAgICAgICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcblxuICAgICAgICAgICAgICAvLyBTaSBoYXkgZmVjaGEgc2VsZWNjaW9uYWRhIHkgZXMgYW50ZXJpb3IgYSBob3ksIHBlcm1pdGlybGEgcGFyYSBlZGljacOzblxuICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWREYXRlICYmIHNlbGVjdGVkRGF0ZSA8IHRvZGF5KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbGVjdGVkRGF0ZTtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC8vIFNpIG5vLCB1c2FyIGZlY2hhIGFjdHVhbCBjb21vIG3DrW5pbW9cbiAgICAgICAgICAgICAgcmV0dXJuIHRvZGF5O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBFbiBtb2RvIGluaWNpYWwsIHNvbG8gZmVjaGFzIGZ1dHVyYXNcbiAgICAgICAgICAgIHJldHVybiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcbiAgICAgICAgICB9O1xuXG4gICAgICAgICAgLy8gREVCVUc6IExvZyBwYXJhIHZlcmlmaWNhciBtaW5EYXRlIGRpbsOhbWljb1xuICAgICAgICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUcgTUlOREFURSBESU7DgU1JQ08gPT09Jyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ2lzRWRpdE1vZGU6JywgaXNFZGl0TW9kZSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ2N1cnJlbnRTdGVwOicsIGN1cnJlbnRTdGVwKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygnc3RlcHMubGVuZ3RoOicsIHN0ZXBzLmxlbmd0aCk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ2Zvcm1EYXRhLmFwcG9pbnRtZW50X2RhdGU6JywgZm9ybURhdGEuYXBwb2ludG1lbnRfZGF0ZSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ2dldE1pbkRhdGUoKSByZXN1bHRhZG86JywgZ2V0TWluRGF0ZSgpKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygnPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0nKTtcblxuICAgICAgICAgIHJldHVybiBjdXJyZW50U3RlcCA9PT0gZGF0ZVN0ZXBJbmRleCAmJiBib29raW5nRmxvdyA9PT0gJ3BlcnNvbmFsaXplZCcgJiYgKFxuICAgICAgICAgICAgPFdlZWtseUF2YWlsYWJpbGl0eVNlbGVjdG9yXG4gICAgICAgICAgICAgIHRpdGxlPVwiwr9DdcOhbmRvIHRlIGd1c3RhcsOtYSBsYSBjaXRhP1wiXG4gICAgICAgICAgICAgIHN1YnRpdGxlPVwiU2VsZWNjaW9uYSBsYSBmZWNoYSBxdWUgbWVqb3IgdGUgY29udmVuZ2FcIlxuICAgICAgICAgICAgICBzZWxlY3RlZERhdGU9e2Zvcm1EYXRhLmFwcG9pbnRtZW50X2RhdGV9XG4gICAgICAgICAgICAgIG9uRGF0ZVNlbGVjdD17aGFuZGxlRGF0ZVNlbGVjdH1cbiAgICAgICAgICAgICAgb3JnYW5pemF0aW9uSWQ9e29yZ2FuaXphdGlvbklkfVxuICAgICAgICAgICAgICBzZXJ2aWNlSWQ9e2Zvcm1EYXRhLnNlcnZpY2VfaWR9XG4gICAgICAgICAgICAgIGRvY3RvcklkPXtmb3JtRGF0YS5kb2N0b3JfaWR9XG4gICAgICAgICAgICAgIGxvY2F0aW9uSWQ9e2Zvcm1EYXRhLmxvY2F0aW9uX2lkfVxuICAgICAgICAgICAgICBtaW5EYXRlPXtnZXRNaW5EYXRlKCl9XG4gICAgICAgICAgICAgIHNob3dEZW5zaXR5SW5kaWNhdG9ycz17dHJ1ZX1cbiAgICAgICAgICAgICAgZW5hYmxlU21hcnRTdWdnZXN0aW9ucz17bW9kZSA9PT0gJ2FpJyAmJiAhIWFpQ29udGV4dH1cbiAgICAgICAgICAgICAgYWlDb250ZXh0PXthaUNvbnRleHQgfHwgdW5kZWZpbmVkfVxuICAgICAgICAgICAgICBlbnRyeU1vZGU9e21vZGV9XG4gICAgICAgICAgICAgIG9uTG9hZEF2YWlsYWJpbGl0eT17bG9hZFdlZWtseUF2YWlsYWJpbGl0eX1cbiAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSkoKX1cblxuICAgICAgICB7LyogVGltZSBTZWxlY3Rpb24gKFBlcnNvbmFsaXplZCBGbG93KSAqL31cbiAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RlcHMgPSBnZXRTdGVwcygpO1xuICAgICAgICAgIGNvbnN0IHRpbWVTdGVwSW5kZXggPSBzdGVwcy5maW5kSW5kZXgoc3RlcCA9PiBzdGVwLmlkID09PSAndGltZScpO1xuICAgICAgICAgIHJldHVybiBjdXJyZW50U3RlcCA9PT0gdGltZVN0ZXBJbmRleCAmJiBib29raW5nRmxvdyA9PT0gJ3BlcnNvbmFsaXplZCcgJiYgKFxuICAgICAgICAgICAgPEVuaGFuY2VkVGltZVNsb3RTZWxlY3RvclxuICAgICAgICAgICAgICB0aXRsZT17YEhvcmFyaW9zIGRpc3BvbmlibGVzIHBhcmEgJHtmb3JtRGF0YS5hcHBvaW50bWVudF9kYXRlfWB9XG4gICAgICAgICAgICAgIHN1YnRpdGxlPVwiU2VsZWNjaW9uYSBlbCBob3JhcmlvIHF1ZSBwcmVmaWVyYXNcIlxuICAgICAgICAgICAgICBzbG90cz17YXZhaWxhYmlsaXR5fVxuICAgICAgICAgICAgICBzZWxlY3RlZFNsb3Q9e3NlbGVjdGVkU2xvdH1cbiAgICAgICAgICAgICAgb25TbG90U2VsZWN0PXtoYW5kbGVTbG90U2VsZWN0fVxuICAgICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgICAgICBlbXB0eU1lc3NhZ2U9XCJObyBoYXkgaG9yYXJpb3MgZGlzcG9uaWJsZXMgcGFyYSBlc3RhIGZlY2hhLiBJbnRlbnRhIGNvbiBvdHJhIGZlY2hhLlwiXG4gICAgICAgICAgICAgIHNob3dEb2N0b3JJbmZvPXshZm9ybURhdGEuZG9jdG9yX2lkfVxuICAgICAgICAgICAgICBzaG93UHJpY2luZz17dHJ1ZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSkoKX1cblxuICAgICAgICB7LyogRXhwcmVzcyBDb25maXJtYXRpb24gKi99XG4gICAgICAgIHsoKCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHN0ZXBzID0gZ2V0U3RlcHMoKTtcbiAgICAgICAgICBjb25zdCBjb25maXJtU3RlcEluZGV4ID0gc3RlcHMuZmluZEluZGV4KHN0ZXAgPT4gc3RlcC5pZCA9PT0gJ2NvbmZpcm0nKTtcbiAgICAgICAgICByZXR1cm4gY3VycmVudFN0ZXAgPT09IGNvbmZpcm1TdGVwSW5kZXggJiYgYm9va2luZ0Zsb3cgPT09ICdleHByZXNzJyAmJiBvcHRpbWFsQXBwb2ludG1lbnQgJiYgKFxuICAgICAgICAgICAgPEV4cHJlc3NDb25maXJtYXRpb25cbiAgICAgICAgICAgICAgYXBwb2ludG1lbnQ9e29wdGltYWxBcHBvaW50bWVudH1cbiAgICAgICAgICAgICAgb25Db25maXJtPXtoYW5kbGVFeHByZXNzQ29uZmlybX1cbiAgICAgICAgICAgICAgb25DdXN0b21pemU9e2hhbmRsZUN1c3RvbWl6ZUZyb21FeHByZXNzfVxuICAgICAgICAgICAgICBvbkJhY2s9e2hhbmRsZUJhY2tUb0Zsb3dTZWxlY3Rpb259XG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIHJlYXNvbj17Zm9ybURhdGEucmVhc29ufVxuICAgICAgICAgICAgICBub3Rlcz17Zm9ybURhdGEubm90ZXN9XG4gICAgICAgICAgICAgIHBhdGllbnROYW1lPXtwYXRpZW50TmFtZX1cbiAgICAgICAgICAgICAgb25SZWFzb25DaGFuZ2U9eyhyZWFzb24pID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgcmVhc29uIH0pKX1cbiAgICAgICAgICAgICAgb25Ob3Rlc0NoYW5nZT17KG5vdGVzKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG5vdGVzIH0pKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSkoKX1cblxuICAgICAgICB7LyogUGVyc29uYWxpemVkIENvbmZpcm1hdGlvbiAqL31cbiAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RlcHMgPSBnZXRTdGVwcygpO1xuICAgICAgICAgIGNvbnN0IGNvbmZpcm1TdGVwSW5kZXggPSBzdGVwcy5maW5kSW5kZXgoc3RlcCA9PiBzdGVwLmlkID09PSAnY29uZmlybScpO1xuICAgICAgICAgIHJldHVybiBjdXJyZW50U3RlcCA9PT0gY29uZmlybVN0ZXBJbmRleCAmJiBib29raW5nRmxvdyA9PT0gJ3BlcnNvbmFsaXplZCcgJiYgc2VsZWN0ZWRTbG90ICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MCBwLTYgcm91bmRlZC14bCBib3JkZXItMiBib3JkZXItYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgMTJsMiAyIDQtNG02IDJhOSA5IDAgMTEtMTggMCA5IDkgMCAwMTE4IDB6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+Q29uZmlybWFyIHR1IGNpdGE8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5SZXZpc2EgbG9zIGRldGFsbGVzIHkgY29uZmlybWEgdHUgY2l0YSBtw6lkaWNhPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8QXBwb2ludG1lbnRTdW1tYXJ5XG4gICAgICAgICAgICAgICAgc2VydmljZT17c2VydmljZXMuZmluZChzID0+IHMuaWQgPT09IGZvcm1EYXRhLnNlcnZpY2VfaWQpPy5uYW1lfVxuICAgICAgICAgICAgICAgIGRvY3Rvcj17c2VsZWN0ZWRTbG90LmRvY3Rvcl9uYW1lfVxuICAgICAgICAgICAgICAgIGxvY2F0aW9uPXtsb2NhdGlvbnMuZmluZChsID0+IGwuaWQgPT09IGZvcm1EYXRhLmxvY2F0aW9uX2lkKT8ubmFtZX1cbiAgICAgICAgICAgICAgICBkYXRlPXtmb3JtRGF0YS5hcHBvaW50bWVudF9kYXRlfVxuICAgICAgICAgICAgICAgIHRpbWU9e2Zvcm1EYXRhLmFwcG9pbnRtZW50X3RpbWV9XG4gICAgICAgICAgICAgICAgc3BlY2lhbGl6YXRpb249e3NlbGVjdGVkU2xvdC5zcGVjaWFsaXphdGlvbn1cbiAgICAgICAgICAgICAgICBwcmljZT17c2VsZWN0ZWRTbG90LmNvbnN1bHRhdGlvbl9mZWV9XG4gICAgICAgICAgICAgICAgcmVhc29uPXtmb3JtRGF0YS5yZWFzb259XG4gICAgICAgICAgICAgICAgbm90ZXM9e2Zvcm1EYXRhLm5vdGVzfVxuICAgICAgICAgICAgICAgIHBhdGllbnROYW1lPXtwYXRpZW50TmFtZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi02XCJcbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICB7LyogUmVhc29uIGFuZCBOb3RlcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIE1vdGl2byBkZSBsYSBjb25zdWx0YSAob3BjaW9uYWwpXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnJlYXNvbn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHJlYXNvbjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHB4LTMgcHktMiBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiBDb25zdWx0YSBnZW5lcmFsLCBkb2xvciBkZSBjYWJlemEsIGV0Yy5cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgTm90YXMgYWRpY2lvbmFsZXMgKG9wY2lvbmFsKVxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubm90ZXN9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBub3RlczogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHB4LTMgcHktMiBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkluZm9ybWFjacOzbiBhZGljaW9uYWwgcXVlIGNvbnNpZGVyZXMgaW1wb3J0YW50ZS4uLlwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQ29uZmlybSBCdXR0b24gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQm9va0FwcG9pbnRtZW50fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWluZGlnby02MDAgdGV4dC13aGl0ZSBweS00IHB4LTggcm91bmRlZC14bCBob3Zlcjpmcm9tLWJsdWUtNzAwIGhvdmVyOnRvLWluZGlnby03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1sZyBmb250LXNlbWlib2xkIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLVsxLjAyXVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLWItMiBib3JkZXItd2hpdGUgbXItM1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIEFnZW5kYW5kbyB0dSBjaXRhLi4uXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTUgMTNsNCA0TDE5IDdcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIENvbmZpcm1hciBDaXRhXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgQWwgY29uZmlybWFyLCByZWNpYmlyw6FzIHVuIGVtYWlsIGNvbiBsb3MgZGV0YWxsZXMgZGUgdHUgY2l0YVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApO1xuICAgICAgICB9KSgpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBGb290ZXIgTmF2aWdhdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBweC02IHB5LTQgZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHJvdW5kZWQtYi1sZ1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgIHtjdXJyZW50U3RlcCA+IDAgJiYgKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQmFja31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwIHRyYW5zaXRpb24tY29sb3JzIHB4LTMgcHktMiByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICBBbnRlcmlvclxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNhbmNlbH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDAgdHJhbnNpdGlvbi1jb2xvcnMgcHgtMyBweS0yIHJvdW5kZWQtbGcgaG92ZXI6YmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCBob3Zlcjpib3JkZXItcmVkLTMwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICBDYW5jZWxhclxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgIFBhc28ge2N1cnJlbnRTdGVwICsgMX0gZGUge2dldFN0ZXBzKCkubGVuZ3RofVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Y3VycmVudFN0ZXAgPCBnZXRTdGVwcygpLmxlbmd0aCAtIDEgJiYgKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV4dH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBweC00IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgaG92ZXI6Ym9yZGVyLWJsdWUtMzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBTaWd1aWVudGVcbiAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgbWwtMVwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cblxuICAgIHsvKiBDb25maXJtYXRpb24gRGlhbG9nICovfVxuICAgIDxDb25maXJtYXRpb25EaWFsb2dcbiAgICAgIGlzT3Blbj17c2hvd0NhbmNlbERpYWxvZ31cbiAgICAgIHRpdGxlPVwiQ2FuY2VsYXIgcmVzZXJ2YSBkZSBjaXRhXCJcbiAgICAgIG1lc3NhZ2U9XCLCv0VzdMOhcyBzZWd1cm8gZGUgcXVlIHF1aWVyZXMgY2FuY2VsYXI/IFNlIHBlcmRlcsOhIHRvZGEgbGEgaW5mb3JtYWNpw7NuIGluZ3Jlc2FkYSB5IHRlbmRyw6FzIHF1ZSBlbXBlemFyIGRlIG51ZXZvLlwiXG4gICAgICBjb25maXJtVGV4dD1cIlPDrSwgY2FuY2VsYXJcIlxuICAgICAgY2FuY2VsVGV4dD1cIkNvbnRpbnVhciByZXNlcnZhbmRvXCJcbiAgICAgIGNvbmZpcm1WYXJpYW50PVwiZGFuZ2VyXCJcbiAgICAgIG9uQ29uZmlybT17aGFuZGxlQ29uZmlybUNhbmNlbH1cbiAgICAgIG9uQ2FuY2VsPXtoYW5kbGVDYW5jZWxEaWFsb2d9XG4gICAgLz5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQXJyb3dMZWZ0IiwiQXJyb3dSaWdodCIsIlByb2dyZXNzSW5kaWNhdG9yIiwiU2VsZWN0aW9uQ2FyZCIsIkFsZXJ0TWVzc2FnZSIsIkFwcG9pbnRtZW50U3VtbWFyeSIsIkNvbmZpcm1hdGlvbkRpYWxvZyIsIkZsb3dTZWxlY3RvciIsIkV4cHJlc3NDb25maXJtYXRpb24iLCJFeHByZXNzU2VhcmNoaW5nU3RhdGUiLCJFbmhhbmNlZFRpbWVTbG90U2VsZWN0b3IiLCJXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvciIsIk9wdGltYWxBcHBvaW50bWVudEZpbmRlciIsIlVuaWZpZWRBcHBvaW50bWVudEZsb3ciLCJvcmdhbml6YXRpb25JZCIsInVzZXJJZCIsInBhdGllbnROYW1lIiwib25BcHBvaW50bWVudEJvb2tlZCIsIm9uQ2FuY2VsIiwiaW5pdGlhbERhdGEiLCJtb2RlIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNob3dDYW5jZWxEaWFsb2ciLCJzZXRTaG93Q2FuY2VsRGlhbG9nIiwiYm9va2luZ0Zsb3ciLCJzZXRCb29raW5nRmxvdyIsIm9wdGltYWxBcHBvaW50bWVudCIsInNldE9wdGltYWxBcHBvaW50bWVudCIsImlzU2VhcmNoaW5nT3B0aW1hbCIsInNldElzU2VhcmNoaW5nT3B0aW1hbCIsImFpQ29udGV4dCIsInNldEFpQ29udGV4dCIsInNlcnZpY2VzIiwic2V0U2VydmljZXMiLCJkb2N0b3JzIiwic2V0RG9jdG9ycyIsImxvY2F0aW9ucyIsInNldExvY2F0aW9ucyIsImF2YWlsYWJpbGl0eSIsInNldEF2YWlsYWJpbGl0eSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJzZXJ2aWNlX2lkIiwiZG9jdG9yX2lkIiwibG9jYXRpb25faWQiLCJhcHBvaW50bWVudF9kYXRlIiwiYXBwb2ludG1lbnRfdGltZSIsInJlYXNvbiIsIm5vdGVzIiwic2VsZWN0ZWRTbG90Iiwic2V0U2VsZWN0ZWRTbG90IiwiZ2V0U3RlcHMiLCJpZCIsInRpdGxlIiwiY29tcGxldGVkIiwiY3VycmVudCIsInVwZGF0ZVN0ZXBzIiwic3RlcHMiLCJtYXAiLCJzdGVwIiwiaW5kZXgiLCJsb2FkU2VydmljZXMiLCJsb2FkTG9jYXRpb25zIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiZGF0YSIsImpzb24iLCJlcnIiLCJjb25zb2xlIiwibG9hZERvY3RvcnMiLCJzZXJ2aWNlSWQiLCJ1cmwiLCJsb2ciLCJsZW5ndGgiLCJsb2FkQXZhaWxhYmlsaXR5IiwibG9hZFdlZWtseUF2YWlsYWJpbGl0eSIsInBhcmFtcyIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJkb2N0b3JJZCIsImxvY2F0aW9uSWQiLCJFcnJvciIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJyZXN1bHQiLCJzdWNjZXNzIiwiZGF5TmFtZXMiLCJ0b2RheSIsIkRhdGUiLCJ0b21vcnJvdyIsInNldERhdGUiLCJnZXREYXRlIiwicHJvY2Vzc2VkRGF0YSIsImRhdGUiLCJkYXRlU3RyaW5nIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImRheURhdGEiLCJhdmFpbGFibGVTbG90cyIsImlzVG9kYXkiLCJ0b0RhdGVTdHJpbmciLCJpc1RvbW9ycm93IiwiaXNXZWVrZW5kIiwiZ2V0RGF5IiwiYXZhaWxhYmlsaXR5TGV2ZWwiLCJwdXNoIiwiZGF5TmFtZSIsInNsb3RzQ291bnQiLCJzbG90cyIsImhhbmRsZU5leHQiLCJoYW5kbGVCYWNrIiwibmV3U3RlcCIsImZsb3dTdGVwSW5kZXgiLCJmaW5kSW5kZXgiLCJoYW5kbGVDYW5jZWwiLCJoYW5kbGVDb25maXJtQ2FuY2VsIiwiaGFuZGxlQ2FuY2VsRGlhbG9nIiwiaGFuZGxlU2VydmljZVNlbGVjdCIsIm9wdGlvbiIsInByZXYiLCJoYW5kbGVEb2N0b3JTZWxlY3QiLCJoYW5kbGVMb2NhdGlvblNlbGVjdCIsImhhbmRsZURhdGVTZWxlY3QiLCJoYW5kbGVTbG90U2VsZWN0Iiwic2xvdCIsInN0YXJ0X3RpbWUiLCJoYW5kbGVGbG93U2VsZWN0IiwiZmxvd1R5cGUiLCJmaW5kT3B0aW1hbEFwcG9pbnRtZW50Iiwic2VhcmNoUHJvbWlzZSIsInByZWZlcmVuY2VzIiwibWF4RGF5c091dCIsInRpbWVQcmVmZXJlbmNlIiwiUHJvbWlzZSIsImFsbCIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiYXBwb2ludG1lbnQiLCJzdGFydFRpbWUiLCJoYW5kbGVFeHByZXNzQ29uZmlybSIsImJvb2tpbmdEYXRhIiwicGF0aWVudElkIiwiYXBwb2ludG1lbnREYXRlIiwiZW5kVGltZSIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImFwcG9pbnRtZW50SWQiLCJtZXNzYWdlIiwiaGFuZGxlQ3VzdG9taXplRnJvbUV4cHJlc3MiLCJoYW5kbGVCYWNrVG9GbG93U2VsZWN0aW9uIiwiaGFuZGxlQ2FuY2VsRXhwcmVzc1NlYXJjaCIsInRpbWVTdGVwSW5kZXgiLCJ3YXJuIiwiaGFuZGxlQm9va0FwcG9pbnRtZW50IiwiZW5kX3RpbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJ0eXBlIiwib25EaXNtaXNzIiwic3VidGl0bGUiLCJvcHRpb25zIiwic2VydmljZSIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsInByaWNlIiwic2VsZWN0ZWRJZCIsIm9uU2VsZWN0IiwiZW1wdHlNZXNzYWdlIiwiZ3JpZENvbHMiLCJvbkZsb3dTZWxlY3QiLCJkb2N0b3JTdGVwSW5kZXgiLCJkb2N0b3IiLCJwcm9maWxlcyIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJzcGVjaWFsaXphdGlvbiIsImNvbnN1bHRhdGlvbl9mZWUiLCJsb2NhdGlvblN0ZXBJbmRleCIsImxvY2F0aW9uIiwiYWRkcmVzcyIsImRhdGVTdGVwSW5kZXgiLCJpc0VkaXRNb2RlIiwiZ2V0TWluRGF0ZSIsInNlbGVjdGVkRGF0ZSIsIm9uRGF0ZVNlbGVjdCIsIm1pbkRhdGUiLCJzaG93RGVuc2l0eUluZGljYXRvcnMiLCJlbmFibGVTbWFydFN1Z2dlc3Rpb25zIiwidW5kZWZpbmVkIiwiZW50cnlNb2RlIiwib25Mb2FkQXZhaWxhYmlsaXR5Iiwib25TbG90U2VsZWN0Iiwic2hvd0RvY3RvckluZm8iLCJzaG93UHJpY2luZyIsImNvbmZpcm1TdGVwSW5kZXgiLCJvbkNvbmZpcm0iLCJvbkN1c3RvbWl6ZSIsIm9uQmFjayIsIm9uUmVhc29uQ2hhbmdlIiwib25Ob3Rlc0NoYW5nZSIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImgzIiwicCIsImZpbmQiLCJzIiwiZG9jdG9yX25hbWUiLCJsIiwidGltZSIsImxhYmVsIiwiaW5wdXQiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwidGV4dGFyZWEiLCJyb3dzIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiaXNPcGVuIiwiY29uZmlybVRleHQiLCJjYW5jZWxUZXh0IiwiY29uZmlybVZhcmlhbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/UnifiedAppointmentFlow.tsx\n"));

/***/ })

});