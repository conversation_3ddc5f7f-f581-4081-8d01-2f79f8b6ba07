"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/appointments/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/(dashboard)/appointments/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AppointmentsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _contexts_tenant_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/tenant-context */ \"(app-pages-browser)/./src/contexts/tenant-context.tsx\");\n/* harmony import */ var _app_api_appointments_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/api/appointments/actions */ \"(app-pages-browser)/./src/app/api/appointments/actions.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_appointments_AppointmentCard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/appointments/AppointmentCard */ \"(app-pages-browser)/./src/components/appointments/AppointmentCard.tsx\");\n/* harmony import */ var _components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/appointments/AppointmentTabs */ \"(app-pages-browser)/./src/components/appointments/AppointmentTabs.tsx\");\n/* harmony import */ var _components_appointments_DateGroupHeader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/appointments/DateGroupHeader */ \"(app-pages-browser)/./src/components/appointments/DateGroupHeader.tsx\");\n/* harmony import */ var _components_appointments_AIEnhancedRescheduleModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/appointments/AIEnhancedRescheduleModal */ \"(app-pages-browser)/./src/components/appointments/AIEnhancedRescheduleModal.tsx\");\n/* harmony import */ var _components_appointments_CancelAppointmentModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/appointments/CancelAppointmentModal */ \"(app-pages-browser)/./src/components/appointments/CancelAppointmentModal.tsx\");\n/* harmony import */ var _utils_dateGrouping__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/dateGrouping */ \"(app-pages-browser)/./src/utils/dateGrouping.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AppointmentsPage() {\n    _s();\n    const { user, profile } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { organization } = (0,_contexts_tenant_context__WEBPACK_IMPORTED_MODULE_5__.useTenant)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize tab based on URL parameter 'view' for backward compatibility\n    const getInitialTab = ()=>{\n        const viewParam = searchParams.get(\"view\");\n        if (viewParam === \"history\") return \"historial\";\n        return \"vigentes\" // default\n        ;\n    };\n    // Use the custom hook for tab management\n    const { activeTab, handleTabChange } = (0,_components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__.useAppointmentTabs)(getInitialTab());\n    // Modal states\n    const [rescheduleModal, setRescheduleModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        appointment: null\n    });\n    const [cancelModal, setCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        appointment: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAppointments();\n    }, [\n        profile,\n        organization\n    ]);\n    const loadAppointments = async ()=>{\n        if (!profile || !organization) return;\n        setIsLoading(true);\n        try {\n            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createClient)();\n            let query = supabase.from(\"appointments\").select(\"\\n          id,\\n          appointment_date,\\n          start_time,\\n          duration_minutes,\\n          status,\\n          reason,\\n          notes,\\n          doctor:doctors!appointments_doctor_id_fkey(\\n            id,\\n            specialization,\\n            profiles(first_name, last_name)\\n          ),\\n          patient:profiles!appointments_patient_id_fkey(\\n            id,\\n            first_name,\\n            last_name\\n          ),\\n          location:locations!appointments_location_id_fkey(\\n            id,\\n            name,\\n            address\\n          ),\\n          service:services!appointments_service_id_fkey(\\n            id,\\n            name,\\n            duration_minutes,\\n            price\\n          )\\n        \").eq(\"organization_id\", organization.id).order(\"appointment_date\", {\n                ascending: true\n            }).order(\"start_time\", {\n                ascending: true\n            });\n            // Filter based on user role\n            if (profile.role === \"patient\") {\n                // Use profile.id directly as patient_id (appointments.patient_id references profiles.id)\n                query = query.eq(\"patient_id\", profile.id);\n            } else if (profile.role === \"doctor\") {\n                // Get doctor record first\n                const { data: doctorData, error: doctorError } = await supabase.from(\"doctors\").select(\"id\").eq(\"profile_id\", profile.id).single();\n                if (doctorError || !doctorData) {\n                    setAppointments([]);\n                    return;\n                }\n                query = query.eq(\"doctor_id\", doctorData.id);\n            }\n            // Load all appointments - filtering will be done in frontend by tabs\n            const { data, error } = await query;\n            if (error) throw error;\n            // Debug: Log appointment data structure to identify doctor name issue\n            if (data && data.length > 0) {\n                var _data_, _data__doctor_, _data__doctor, _data_1, _data__doctor__profiles_, _data__doctor__profiles, _data__doctor_1, _data__doctor1, _data_2, _data__doctor__profiles_1, _data__doctor__profiles1, _data__doctor_2, _data__doctor2, _data_3;\n                console.log(\"\\uD83D\\uDD0D DEBUG - Appointments data structure:\", {\n                    totalAppointments: data.length,\n                    firstAppointment: data[0],\n                    doctorStructure: (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.doctor,\n                    doctorProfiles: (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : (_data__doctor = _data_1.doctor) === null || _data__doctor === void 0 ? void 0 : (_data__doctor_ = _data__doctor[0]) === null || _data__doctor_ === void 0 ? void 0 : _data__doctor_.profiles,\n                    doctorFirstName: (_data_2 = data[0]) === null || _data_2 === void 0 ? void 0 : (_data__doctor1 = _data_2.doctor) === null || _data__doctor1 === void 0 ? void 0 : (_data__doctor_1 = _data__doctor1[0]) === null || _data__doctor_1 === void 0 ? void 0 : (_data__doctor__profiles = _data__doctor_1.profiles) === null || _data__doctor__profiles === void 0 ? void 0 : (_data__doctor__profiles_ = _data__doctor__profiles[0]) === null || _data__doctor__profiles_ === void 0 ? void 0 : _data__doctor__profiles_.first_name,\n                    doctorLastName: (_data_3 = data[0]) === null || _data_3 === void 0 ? void 0 : (_data__doctor2 = _data_3.doctor) === null || _data__doctor2 === void 0 ? void 0 : (_data__doctor_2 = _data__doctor2[0]) === null || _data__doctor_2 === void 0 ? void 0 : (_data__doctor__profiles1 = _data__doctor_2.profiles) === null || _data__doctor__profiles1 === void 0 ? void 0 : (_data__doctor__profiles_1 = _data__doctor__profiles1[0]) === null || _data__doctor__profiles_1 === void 0 ? void 0 : _data__doctor__profiles_1.last_name\n                });\n            }\n            setAppointments(data || []);\n        } catch (err) {\n            console.error(\"Error loading appointments:\", err);\n            setError(\"Error al cargar las citas\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCancelAppointment = (appointmentId)=>{\n        const appointment = appointments.find((apt)=>apt.id === appointmentId);\n        if (appointment) {\n            setCancelModal({\n                isOpen: true,\n                appointment\n            });\n        }\n    };\n    const handleRescheduleAppointment = (appointmentId)=>{\n        const appointment = appointments.find((apt)=>apt.id === appointmentId);\n        if (appointment) {\n            setRescheduleModal({\n                isOpen: true,\n                appointment\n            });\n        }\n    };\n    // Modal handlers\n    const handleConfirmCancellation = async (appointmentId, reason, customReason)=>{\n        try {\n            const result = await (0,_app_api_appointments_actions__WEBPACK_IMPORTED_MODULE_6__.cancelAppointment)(appointmentId);\n            if (result.success) {\n                // Store cancellation analytics\n                await storeCancellationAnalytics(appointmentId, reason, customReason);\n                setCancelModal({\n                    isOpen: false,\n                    appointment: null\n                });\n                loadAppointments() // Reload appointments\n                ;\n                setError(null) // Clear any previous errors\n                ;\n            } else {\n                setError(result.error || \"Error al cancelar la cita\");\n            }\n        } catch (err) {\n            setError(\"Error al cancelar la cita\");\n        }\n    };\n    const storeCancellationAnalytics = async (appointmentId, reason, customReason)=>{\n        try {\n            const appointment = appointments.find((apt)=>apt.id === appointmentId);\n            if (!appointment || !profile || !organization) return;\n            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createClient)();\n            await supabase.from(\"appointment_analytics\").insert({\n                appointment_id: appointmentId,\n                organization_id: organization.id,\n                action_type: \"cancelled\",\n                reason_category: reason,\n                reason_text: customReason || null,\n                original_date: appointment.appointment_date,\n                original_time: appointment.start_time,\n                user_id: profile.id,\n                user_role: profile.role,\n                time_to_action: \"\".concat(Math.floor((new Date().getTime() - new Date(appointment.appointment_date).getTime()) / (1000 * 60 * 60 * 24)), \" days\")\n            });\n        } catch (error) {\n            console.error(\"Error storing cancellation analytics:\", error);\n        // Don't throw error to avoid disrupting the cancellation flow\n        }\n    };\n    const handleConfirmReschedule = async (appointmentId, newDate, newTime)=>{\n        try {\n            const result = await (0,_app_api_appointments_actions__WEBPACK_IMPORTED_MODULE_6__.updateAppointment)({\n                id: appointmentId,\n                appointment_date: newDate,\n                start_time: newTime\n            });\n            if (result.success) {\n                // Store reschedule analytics\n                await storeRescheduleAnalytics(appointmentId, newDate, newTime);\n                setRescheduleModal({\n                    isOpen: false,\n                    appointment: null\n                });\n                loadAppointments() // Reload appointments\n                ;\n                setError(null) // Clear any previous errors\n                ;\n            } else {\n                setError(result.error || \"Error al reagendar la cita\");\n            }\n        } catch (err) {\n            setError(\"Error al reagendar la cita\");\n        }\n    };\n    const storeRescheduleAnalytics = async (appointmentId, newDate, newTime)=>{\n        try {\n            const appointment = appointments.find((apt)=>apt.id === appointmentId);\n            if (!appointment || !profile || !organization) return;\n            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createClient)();\n            await supabase.from(\"appointment_analytics\").insert({\n                appointment_id: appointmentId,\n                organization_id: organization.id,\n                action_type: \"rescheduled\",\n                original_date: appointment.appointment_date,\n                original_time: appointment.start_time,\n                new_date: newDate,\n                new_time: newTime,\n                user_id: profile.id,\n                user_role: profile.role,\n                time_to_action: \"\".concat(Math.floor((new Date().getTime() - new Date(appointment.appointment_date).getTime()) / (1000 * 60 * 60 * 24)), \" days\")\n            });\n        } catch (error) {\n            console.error(\"Error storing reschedule analytics:\", error);\n        // Don't throw error to avoid disrupting the reschedule flow\n        }\n    };\n    const handleStatusChange = async (appointmentId, newStatus)=>{\n        try {\n            const result = await (0,_app_api_appointments_actions__WEBPACK_IMPORTED_MODULE_6__.updateAppointment)({\n                id: appointmentId,\n                status: newStatus\n            });\n            if (result.success) {\n                loadAppointments() // Reload appointments\n                ;\n            } else {\n                setError(result.error || \"Error al actualizar la cita\");\n            }\n        } catch (err) {\n            setError(\"Error al actualizar la cita\");\n        }\n    };\n    // Status functions moved to AppointmentCard component\n    const canCancelAppointment = (appointment)=>{\n        // Check if appointment is in the future\n        const appointmentDateTime = new Date(\"\".concat(appointment.appointment_date, \"T\").concat(appointment.start_time));\n        const now = new Date();\n        const isFuture = appointmentDateTime > now;\n        // Check if status allows cancellation (simplified states)\n        const cancellableStatuses = [\n            \"confirmed\",\n            \"pending\"\n        ];\n        const isStatusCancellable = cancellableStatuses.includes(appointment.status);\n        // Check user permissions\n        let hasPermission = false;\n        if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\") {\n            // For patients: appointments are already filtered by patient_id = profile.id in loadAppointments()\n            // So if the appointment is loaded, the patient owns it and has permission\n            hasPermission = true;\n        } else if ([\n            \"admin\",\n            \"staff\",\n            \"doctor\"\n        ].includes((profile === null || profile === void 0 ? void 0 : profile.role) || \"\")) {\n            // Admin, staff, and doctors can cancel appointments in their organization\n            hasPermission = true // Already filtered by organization in query\n            ;\n        } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"superadmin\") {\n            // SuperAdmin can cancel any appointment\n            hasPermission = true;\n        }\n        return isFuture && isStatusCancellable && hasPermission;\n    };\n    const canRescheduleAppointment = (appointment)=>{\n        // Check if appointment is in the future\n        const appointmentDateTime = new Date(\"\".concat(appointment.appointment_date, \"T\").concat(appointment.start_time));\n        const now = new Date();\n        const isFuture = appointmentDateTime > now;\n        // Check if status allows rescheduling (simplified states)\n        const reschedulableStatuses = [\n            \"confirmed\",\n            \"pending\"\n        ];\n        const isStatusReschedulable = reschedulableStatuses.includes(appointment.status);\n        // Check user permissions (same as cancellation)\n        let hasPermission = false;\n        if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\") {\n            // For patients: appointments are already filtered by patient_id = profile.id in loadAppointments()\n            // So if the appointment is loaded, the patient owns it and has permission\n            hasPermission = true;\n        } else if ([\n            \"admin\",\n            \"staff\",\n            \"doctor\"\n        ].includes((profile === null || profile === void 0 ? void 0 : profile.role) || \"\")) {\n            hasPermission = true;\n        } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === \"superadmin\") {\n            hasPermission = true;\n        }\n        return isFuture && isStatusReschedulable && hasPermission;\n    };\n    const canChangeStatus = (appointment)=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.role) && [\n            \"admin\",\n            \"staff\",\n            \"doctor\",\n            \"superadmin\"\n        ].includes(profile.role);\n    };\n    // Determine page title and actions based on role\n    const getPageTitle = ()=>{\n        switch(profile === null || profile === void 0 ? void 0 : profile.role){\n            case \"patient\":\n                return \"Mis Citas\";\n            case \"doctor\":\n                return \"Agenda de Citas\";\n            case \"admin\":\n            case \"staff\":\n                return \"Gesti\\xf3n de Citas\";\n            default:\n                return \"Citas\";\n        }\n    };\n    const getPageSubtitle = ()=>{\n        const filteredAppointments = (0,_components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__.filterAppointmentsByTab)(appointments, activeTab);\n        const tabText = activeTab === \"vigentes\" ? \"vigentes\" : \"en historial\";\n        return \"\".concat(filteredAppointments.length, \" citas \").concat(tabText, \" • \").concat((organization === null || organization === void 0 ? void 0 : organization.name) || \"\");\n    };\n    const actions = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: ((profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\" || [\n            \"admin\",\n            \"staff\",\n            \"doctor\"\n        ].includes((profile === null || profile === void 0 ? void 0 : profile.role) || \"\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            href: \"/appointments/book\",\n            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, this),\n                (profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\" ? \"Agendar Cita\" : \"Nueva Cita\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n            lineNumber: 353,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            title: getPageTitle(),\n            subtitle: \"Cargando...\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Cargando citas...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                lineNumber: 367,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        title: getPageTitle(),\n        subtitle: getPageSubtitle(),\n        actions: actions,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Error\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this),\n                    (profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: appointments,\n                            activeTab: activeTab,\n                            onTabChange: handleTabChange,\n                            loading: isLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this),\n                    (()=>{\n                        // Filter appointments based on active tab for patients, show all for other roles\n                        const filteredAppointments = (profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\" ? (0,_components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__.filterAppointmentsByTab)(appointments, activeTab) : appointments;\n                        if (appointments.length === 0) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg px-6 py-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No hay citas registradas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 mb-4\",\n                                        children: \"A\\xfan no tienes citas registradas en el sistema.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\" || [\n                                        \"admin\",\n                                        \"staff\",\n                                        \"doctor\"\n                                    ].includes((profile === null || profile === void 0 ? void 0 : profile.role) || \"\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/appointments/book\",\n                                        className: \"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 21\n                                            }, this),\n                                            (profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\" ? \"Agendar Primera Cita\" : \"Crear Nueva Cita\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        if (filteredAppointments.length === 0 && (profile === null || profile === void 0 ? void 0 : profile.role) === \"patient\") {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__.EmptyTabMessage, {\n                                tabType: activeTab,\n                                onCreateAppointment: ()=>window.location.href = \"/appointments/book\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: (()=>{\n                                const groupedAppointments = (0,_utils_dateGrouping__WEBPACK_IMPORTED_MODULE_14__.groupAppointmentsByDate)(filteredAppointments);\n                                const sortedGroupKeys = (0,_utils_dateGrouping__WEBPACK_IMPORTED_MODULE_14__.getSortedGroupKeys)(groupedAppointments);\n                                return sortedGroupKeys.map((groupKey)=>{\n                                    const group = groupedAppointments[groupKey];\n                                    const headerInfo = (0,_utils_dateGrouping__WEBPACK_IMPORTED_MODULE_14__.getDateHeader)(groupKey, group.label);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointments_DateGroupHeader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                title: headerInfo.title,\n                                                subtitle: headerInfo.subtitle,\n                                                icon: headerInfo.icon,\n                                                appointmentCount: group.appointments.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: group.appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointments_AppointmentCard__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        appointment: appointment,\n                                                        userRole: (profile === null || profile === void 0 ? void 0 : profile.role) || \"patient\",\n                                                        onReschedule: handleRescheduleAppointment,\n                                                        onCancel: handleCancelAppointment,\n                                                        onStatusChange: handleStatusChange,\n                                                        canReschedule: canRescheduleAppointment(appointment),\n                                                        canCancel: canCancelAppointment(appointment),\n                                                        canChangeStatus: canChangeStatus(appointment),\n                                                        showLocation: true,\n                                                        showCost: (profile === null || profile === void 0 ? void 0 : profile.role) !== \"patient\",\n                                                        showDuration: true\n                                                    }, appointment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, groupKey, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 19\n                                    }, this);\n                                });\n                            })()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this);\n                    })()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointments_AIEnhancedRescheduleModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: rescheduleModal.isOpen,\n                appointment: rescheduleModal.appointment,\n                organizationId: (organization === null || organization === void 0 ? void 0 : organization.id) || \"\",\n                onConfirm: handleConfirmReschedule,\n                onCancel: ()=>setRescheduleModal({\n                        isOpen: false,\n                        appointment: null\n                    }),\n                onCancelAppointment: handleConfirmCancellation,\n                loading: isLoading,\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                lineNumber: 495,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointments_CancelAppointmentModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: cancelModal.isOpen,\n                appointment: cancelModal.appointment,\n                onConfirm: handleConfirmCancellation,\n                onCancel: ()=>setCancelModal({\n                        isOpen: false,\n                        appointment: null\n                    }),\n                loading: isLoading,\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\(dashboard)\\\\appointments\\\\page.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentsPage, \"UkY/+xKT7TWnMP+TAvHUm3ahuCQ=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_tenant_context__WEBPACK_IMPORTED_MODULE_5__.useTenant,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _components_appointments_AppointmentTabs__WEBPACK_IMPORTED_MODULE_10__.useAppointmentTabs\n    ];\n});\n_c = AppointmentsPage;\nvar _c;\n$RefreshReg$(_c, \"AppointmentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/appointments/page.tsx\n"));

/***/ })

});