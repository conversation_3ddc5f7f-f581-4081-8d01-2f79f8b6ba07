"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* harmony import */ var _lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/dateValidation */ \"(app-pages-browser)/./src/lib/utils/dateValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * CRITICAL FEATURE: UI-level date blocking validation\n   * Validates which dates should be blocked based on 4-hour advance booking rule\n   * Prevents user confusion by showing blocked dates as disabled\n   */ const dateValidationResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (weekData.length === 0) return {};\n        const dates = weekData.map((day)=>day.date);\n        // Create mock time slots for validation (typical business hours)\n        const availableSlotsByDate = {};\n        weekData.forEach((day)=>{\n            if (day.availabilityLevel !== \"none\") {\n                // Generate typical business hours for validation\n                availableSlotsByDate[day.date] = [\n                    {\n                        date: day.date,\n                        time: \"08:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"10:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"11:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"14:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"15:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"16:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"17:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"18:00\",\n                        available: true\n                    }\n                ];\n            } else {\n                availableSlotsByDate[day.date] = [];\n            }\n        });\n        console.log(\"=== DEBUG DATE BLOCKING VALIDATION ===\");\n        console.log(\"Validating dates:\", dates);\n        console.log(\"Available slots by date:\", availableSlotsByDate);\n        const validationResults = (0,_lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__.validateMultipleDates)(dates, availableSlotsByDate);\n        console.log(\"Validation results:\", validationResults);\n        console.log(\"========================================\");\n        return validationResults;\n    }, [\n        weekData\n    ]);\n    /**\n   * Enhanced week data with blocking information\n   */ const enhancedWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return weekData.map((day)=>{\n            const validation = dateValidationResults[day.date];\n            const isBlocked = validation && !validation.isValid;\n            return {\n                ...day,\n                isBlocked,\n                blockReason: validation === null || validation === void 0 ? void 0 : validation.reason,\n                validationResult: validation\n            };\n        });\n    }, [\n        weekData,\n        dateValidationResults\n    ]);\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic using timezone-safe objects\n        const utcDateStringFromUTC = dateObjUTC.toISOString().split(\"T\")[0];\n        const utcDateStringFromLocal = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateStringFromUTC;\n        console.log(\"Comparaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString (from UTC obj):\", utcDateStringFromUTC);\n        console.log(\"  - utcDateString (from local obj):\", utcDateStringFromLocal);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"\\xbfDate objects son consistentes?:\", localDateString === date);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 587,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 605,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 600,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 630,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 654,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 639,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 667,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 675,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 694,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 693,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 709,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 736,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 734,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 575,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"14U+tOJsF/03vp4I7+MnLlHkFdQ=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FwcG9pbnRtZW50cy9XZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3Rvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUVBOzs7Ozs7Ozs7Ozs7Ozs7OztDQWlCQyxHQUV3RTtBQUNtQjtBQUNSO0FBQ047QUFDeUM7QUFDekI7QUFrRjlGOztDQUVDLEdBQ0QsTUFBTWUsNEJBQTRCLENBQ2hDQyxXQUNBQyxnQkFDQUMsV0FDQUMsVUFDQUMsWUFDQUM7O0lBRUEsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUd0QiwrQ0FBUUEsQ0FBd0IsRUFBRTtJQUNsRSxNQUFNLENBQUN1QixTQUFTQyxXQUFXLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN5QixPQUFPQyxTQUFTLEdBQUcxQiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsTUFBTTJCLGVBQWV6QixrREFBV0EsQ0FBQztRQUMvQixJQUFJLENBQUNrQixvQkFBb0I7WUFDdkIsc0RBQXNEO1lBQ3RELE1BQU1RLFdBQWtDLEVBQUU7WUFDMUMsTUFBTUMsV0FBVztnQkFBQztnQkFBVztnQkFBUztnQkFBVTtnQkFBYTtnQkFBVTtnQkFBVzthQUFTO1lBRTNGLHdEQUF3RDtZQUN4REMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJoQjtZQUNuQ2UsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQmhCLFVBQVVpQixXQUFXO1lBQ25ERixRQUFRQyxHQUFHLENBQUMsOEJBQThCaEIsVUFBVWtCLGlCQUFpQjtZQUVyRSxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLO2dCQUMxQixtREFBbUQ7Z0JBQ25ELG1GQUFtRjtnQkFDbkYsTUFBTUMsT0FBTyxJQUFJQyxLQUFLckIsVUFBVXNCLFdBQVcsSUFBSXRCLFVBQVV1QixRQUFRLElBQUl2QixVQUFVd0IsT0FBTyxLQUFLTDtnQkFFM0YsOEJBQThCO2dCQUM5QkosUUFBUUMsR0FBRyxDQUFDLFVBQVMsT0FBRkcsR0FBRSx5QkFBb0I7b0JBQ3ZDTSxlQUFlekIsVUFBVXNCLFdBQVc7b0JBQ3BDSSxnQkFBZ0IxQixVQUFVdUIsUUFBUTtvQkFDbENJLGVBQWUzQixVQUFVd0IsT0FBTztvQkFDaENJLFFBQVFUO29CQUNSVSxhQUFhN0IsVUFBVXdCLE9BQU8sS0FBS0w7Z0JBQ3JDO2dCQUVBLDhDQUE4QztnQkFDOUNKLFFBQVFDLEdBQUcsQ0FBQyxVQUFTLE9BQUZHLEdBQUUsNENBQW9DO29CQUN2RFcsU0FBU1YsS0FBS0gsV0FBVztvQkFDekJPLFNBQVNKLEtBQUtJLE9BQU87b0JBQ3JCTyxRQUFRWCxLQUFLVyxNQUFNO29CQUNuQkMsU0FBU2xCLFFBQVEsQ0FBQ00sS0FBS1csTUFBTSxHQUFHO29CQUNoQ0UsaUJBQWlCLEdBQXlCQyxPQUF0QmQsS0FBS0UsV0FBVyxJQUFHLEtBQW1EWSxPQUFoREEsT0FBT2QsS0FBS0csUUFBUSxLQUFLLEdBQUdZLFFBQVEsQ0FBQyxHQUFHLE1BQUssS0FBMkMsT0FBeENELE9BQU9kLEtBQUtJLE9BQU8sSUFBSVcsUUFBUSxDQUFDLEdBQUc7Z0JBQy9IO2dCQUVBLE1BQU1DLFFBQVEsSUFBSWY7Z0JBQ2xCZSxNQUFNQyxRQUFRLENBQUMsR0FBRyxHQUFHLEdBQUcsSUFBSSwyQ0FBMkM7Z0JBQ3ZFakIsS0FBS2lCLFFBQVEsQ0FBQyxHQUFHLEdBQUcsR0FBRyxJQUFJLDJDQUEyQztnQkFFdEUsTUFBTUMsVUFBVWxCLEtBQUttQixPQUFPLE9BQU9ILE1BQU1HLE9BQU87Z0JBQ2hELE1BQU1DLGFBQWFwQixLQUFLbUIsT0FBTyxLQUFLSCxNQUFNRyxPQUFPO2dCQUNqRCxNQUFNRSxXQUFXLElBQUlwQixLQUFLZTtnQkFDMUJLLFNBQVNDLE9BQU8sQ0FBQ04sTUFBTVosT0FBTyxLQUFLO2dCQUNuQyxNQUFNbUIsYUFBYXZCLEtBQUttQixPQUFPLE9BQU9FLFNBQVNGLE9BQU87Z0JBRXRELE1BQU1LLFlBQVl4QixLQUFLVyxNQUFNLE9BQU8sS0FBS1gsS0FBS1csTUFBTSxPQUFPO2dCQUUzRCxtREFBbUQ7Z0JBQ25ELElBQUljLGFBQWE7Z0JBQ2pCLElBQUksQ0FBQ0wsWUFBWTtvQkFDZkssYUFBYUQsWUFBWUUsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssS0FBS0YsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUs7Z0JBQ3RGO2dCQUVBLElBQUlDLG9CQUF1QztnQkFDM0MsSUFBSVQsWUFBWTtvQkFDZFMsb0JBQW9CLFFBQVEsNENBQTRDO2dCQUMxRSxPQUFPLElBQUlKLGVBQWUsR0FBRztvQkFDM0JJLG9CQUFvQjtnQkFDdEIsT0FBTyxJQUFJSixjQUFjLEdBQUc7b0JBQzFCSSxvQkFBb0I7Z0JBQ3RCLE9BQU8sSUFBSUosY0FBYyxHQUFHO29CQUMxQkksb0JBQW9CO2dCQUN0QixPQUFPO29CQUNMQSxvQkFBb0I7Z0JBQ3RCO2dCQUVBLGtEQUFrRDtnQkFDbEQsTUFBTUMsa0JBQWtCLEdBQXlCaEIsT0FBdEJkLEtBQUtFLFdBQVcsSUFBRyxLQUFtRFksT0FBaERBLE9BQU9kLEtBQUtHLFFBQVEsS0FBSyxHQUFHWSxRQUFRLENBQUMsR0FBRyxNQUFLLEtBQTJDLE9BQXhDRCxPQUFPZCxLQUFLSSxPQUFPLElBQUlXLFFBQVEsQ0FBQyxHQUFHO2dCQUVwSSwyQ0FBMkM7Z0JBQzNDcEIsUUFBUUMsR0FBRyxDQUFDLFVBQVMsT0FBRkcsR0FBRSxzQkFBb0I7b0JBQ3ZDQyxNQUFNOEI7b0JBQ05DLFNBQVMvQixLQUFLSCxXQUFXLEdBQUdtQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3pDQyxXQUFXSDtvQkFDWGxCLFNBQVNsQixRQUFRLENBQUNNLEtBQUtXLE1BQU0sR0FBRztvQkFDaENjO29CQUNBSTtvQkFDQVg7b0JBQ0FLO29CQUNBQztvQkFDQVUsb0JBQW9CO3dCQUNsQkMsS0FBS25DLEtBQUtILFdBQVcsR0FBR21DLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTt3QkFDckNJLE9BQU9OO3dCQUNQTyxPQUFPckMsS0FBS0gsV0FBVyxHQUFHbUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUtGO29CQUM5QztnQkFDRjtnQkFFQXJDLFNBQVM2QyxJQUFJLENBQUM7b0JBQ1p0QyxNQUFNOEI7b0JBQ05sQixTQUFTbEIsUUFBUSxDQUFDTSxLQUFLVyxNQUFNLEdBQUc7b0JBQ2hDYztvQkFDQUk7b0JBQ0FYO29CQUNBSztvQkFDQUM7Z0JBQ0Y7WUFDRjtZQUVBN0IsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0JIO1lBQ2xDRSxRQUFRQyxHQUFHLENBQUM7WUFFWlQsWUFBWU07WUFDWjtRQUNGO1FBRUFKLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNZ0QsVUFBVSxJQUFJdEMsS0FBS3JCO1lBQ3pCMkQsUUFBUWpCLE9BQU8sQ0FBQzFDLFVBQVV3QixPQUFPLEtBQUs7WUFFdEMsTUFBTW9DLE9BQU8sTUFBTXZELG1CQUFtQjtnQkFDcENKO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBSixXQUFXQSxVQUFVaUIsV0FBVyxHQUFHbUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNoRE8sU0FBU0EsUUFBUTFDLFdBQVcsR0FBR21DLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM5QztZQUVBN0MsWUFBWXFEO1FBQ2QsRUFBRSxPQUFPQyxLQUFLO1lBQ1psRCxTQUFTa0QsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1lBQzlDaEQsUUFBUUwsS0FBSyxDQUFDLG9DQUFvQ21EO1FBQ3BELFNBQVU7WUFDUnBELFdBQVc7UUFDYjtJQUNGLEdBQUc7UUFBQ1Q7UUFBV0M7UUFBZ0JDO1FBQVdDO1FBQVVDO1FBQVlDO0tBQW1CO0lBRW5GbkIsZ0RBQVNBLENBQUM7UUFDUjBCO0lBQ0YsR0FBRztRQUFDQTtLQUFhO0lBRWpCLE9BQU87UUFBRU47UUFBVUU7UUFBU0U7UUFBT3NELFNBQVNwRDtJQUFhO0FBQzNEO0dBcEpNYjtBQXNKTjs7Q0FFQyxHQUNELE1BQU1rRSw2QkFBd0U7UUFBQyxFQUM3RUMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLFlBQVksRUFDWkMsWUFBWSxFQUNacEUsY0FBYyxFQUNkQyxTQUFTLEVBQ1RDLFFBQVEsRUFDUkMsVUFBVSxFQUNWa0UsT0FBTyxFQUNQQyx3QkFBd0IsSUFBSSxFQUM1QkMseUJBQXlCLEtBQUssRUFDOUJDLFNBQVMsRUFDVEMsWUFBWSxRQUFRLEVBQ3BCQyxxQkFBcUIsS0FBSyxFQUMxQnRFLGtCQUFrQixFQUNsQkcsU0FBU29FLGtCQUFrQixLQUFLLEVBQ2hDQyxZQUFZLEVBQUUsRUFDZjs7SUFDQyxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRzlGLCtDQUFRQSxDQUFDO1FBQzdDLE1BQU1tRCxRQUFRLElBQUlmO1FBQ2xCLE1BQU0yRCxjQUFjLElBQUkzRCxLQUFLZTtRQUM3QjRDLFlBQVl0QyxPQUFPLENBQUNOLE1BQU1aLE9BQU8sS0FBS1ksTUFBTUwsTUFBTSxLQUFLLGdDQUFnQztRQUN2RixPQUFPaUQ7SUFDVDtJQUVBLE1BQU0sRUFBRTFFLFFBQVEsRUFBRUUsU0FBU3lFLFdBQVcsRUFBRXZFLEtBQUssRUFBRXNELE9BQU8sRUFBRSxHQUFHakUsMEJBQ3pEK0UsYUFDQTdFLGdCQUNBQyxXQUNBQyxVQUNBQyxZQUNBQztJQUdGLDBCQUEwQjtJQUMxQixNQUFNLENBQUM2RSxrQkFBa0JDLG9CQUFvQixHQUFHbEcsK0NBQVFBLENBQTJCO0lBQ25GLE1BQU0sQ0FBQ21HLG9CQUFvQkMsc0JBQXNCLEdBQUdwRywrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNxRyxzQkFBc0JDLHdCQUF3QixHQUFHdEcsK0NBQVFBLENBQUM7SUFFakUsTUFBTXVHLFlBQVlaLG1CQUFtQks7SUFFckM7Ozs7R0FJQyxHQUNELE1BQU1RLHdCQUF3QnJHLDhDQUFPQSxDQUFDO1FBQ3BDLElBQUlrQixTQUFTb0YsTUFBTSxLQUFLLEdBQUcsT0FBTyxDQUFDO1FBRW5DLE1BQU1DLFFBQVFyRixTQUFTc0YsR0FBRyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJekUsSUFBSTtRQUUxQyxpRUFBaUU7UUFDakUsTUFBTTBFLHVCQUE4QyxDQUFDO1FBQ3JEeEYsU0FBU3lGLE9BQU8sQ0FBQ0YsQ0FBQUE7WUFDZixJQUFJQSxJQUFJNUMsaUJBQWlCLEtBQUssUUFBUTtnQkFDcEMsaURBQWlEO2dCQUNqRDZDLG9CQUFvQixDQUFDRCxJQUFJekUsSUFBSSxDQUFDLEdBQUc7b0JBQy9CO3dCQUFFQSxNQUFNeUUsSUFBSXpFLElBQUk7d0JBQUU0RSxNQUFNO3dCQUFTQyxXQUFXO29CQUFLO29CQUNqRDt3QkFBRTdFLE1BQU15RSxJQUFJekUsSUFBSTt3QkFBRTRFLE1BQU07d0JBQVNDLFdBQVc7b0JBQUs7b0JBQ2pEO3dCQUFFN0UsTUFBTXlFLElBQUl6RSxJQUFJO3dCQUFFNEUsTUFBTTt3QkFBU0MsV0FBVztvQkFBSztvQkFDakQ7d0JBQUU3RSxNQUFNeUUsSUFBSXpFLElBQUk7d0JBQUU0RSxNQUFNO3dCQUFTQyxXQUFXO29CQUFLO29CQUNqRDt3QkFBRTdFLE1BQU15RSxJQUFJekUsSUFBSTt3QkFBRTRFLE1BQU07d0JBQVNDLFdBQVc7b0JBQUs7b0JBQ2pEO3dCQUFFN0UsTUFBTXlFLElBQUl6RSxJQUFJO3dCQUFFNEUsTUFBTTt3QkFBU0MsV0FBVztvQkFBSztvQkFDakQ7d0JBQUU3RSxNQUFNeUUsSUFBSXpFLElBQUk7d0JBQUU0RSxNQUFNO3dCQUFTQyxXQUFXO29CQUFLO29CQUNqRDt3QkFBRTdFLE1BQU15RSxJQUFJekUsSUFBSTt3QkFBRTRFLE1BQU07d0JBQVNDLFdBQVc7b0JBQUs7b0JBQ2pEO3dCQUFFN0UsTUFBTXlFLElBQUl6RSxJQUFJO3dCQUFFNEUsTUFBTTt3QkFBU0MsV0FBVztvQkFBSztpQkFDbEQ7WUFDSCxPQUFPO2dCQUNMSCxvQkFBb0IsQ0FBQ0QsSUFBSXpFLElBQUksQ0FBQyxHQUFHLEVBQUU7WUFDckM7UUFDRjtRQUVBTCxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQjJFO1FBQ2pDNUUsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QjhFO1FBRXhDLE1BQU1JLG9CQUFvQnBHLGdGQUFxQkEsQ0FBQzZGLE9BQU9HO1FBRXZEL0UsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QmtGO1FBQ25DbkYsUUFBUUMsR0FBRyxDQUFDO1FBRVosT0FBT2tGO0lBQ1QsR0FBRztRQUFDNUY7S0FBUztJQUViOztHQUVDLEdBQ0QsTUFBTTZGLG1CQUFtQi9HLDhDQUFPQSxDQUFDO1FBQy9CLE9BQU9rQixTQUFTc0YsR0FBRyxDQUFDQyxDQUFBQTtZQUNsQixNQUFNTyxhQUFhWCxxQkFBcUIsQ0FBQ0ksSUFBSXpFLElBQUksQ0FBQztZQUNsRCxNQUFNaUYsWUFBWUQsY0FBYyxDQUFDQSxXQUFXRSxPQUFPO1lBRW5ELE9BQU87Z0JBQ0wsR0FBR1QsR0FBRztnQkFDTlE7Z0JBQ0FFLFdBQVcsRUFBRUgsdUJBQUFBLGlDQUFBQSxXQUFZSSxNQUFNO2dCQUMvQkMsa0JBQWtCTDtZQUNwQjtRQUNGO0lBQ0YsR0FBRztRQUFDOUY7UUFBVW1GO0tBQXNCO0lBRXBDOztHQUVDLEdBQ0QsTUFBTWlCLGVBQWUsQ0FBQ0M7UUFDcEIsZ0NBQWdDO1FBQ2hDNUYsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxpQkFBYzJGO1FBQzFCNUYsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QjhEO1FBQ25DL0QsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQjhELFlBQVk3RCxXQUFXO1FBQ3ZERixRQUFRQyxHQUFHLENBQUMsaUJBQWlCc0Q7UUFFN0IsTUFBTXNDLFVBQVUsSUFBSXZGLEtBQUt5RDtRQUN6QjhCLFFBQVFsRSxPQUFPLENBQUNvQyxZQUFZdEQsT0FBTyxLQUFNbUYsQ0FBQUEsY0FBYyxTQUFTLElBQUksQ0FBQztRQUVyRTVGLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0I0RjtRQUNsQzdGLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0I0RixRQUFRM0YsV0FBVztRQUUvQyx1QkFBdUI7UUFDdkIsSUFBSXFELFdBQVdxQyxjQUFjLFFBQVE7WUFDbkMsTUFBTUUsYUFBYSxJQUFJeEYsS0FBS2lEO1lBQzVCdkQsUUFBUUMsR0FBRyxDQUFDLGVBQWU2RjtZQUMzQjlGLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBcUM0RixVQUFVQztZQUUzRCxJQUFJRCxVQUFVQyxZQUFZO2dCQUN4QjlGLFFBQVFDLEdBQUcsQ0FBQztnQkFDWkQsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLFFBQVEsK0NBQStDO1lBQ3pEO1FBQ0Y7UUFFQSxpRUFBaUU7UUFDakUsSUFBSTJGLGNBQWMsUUFBUTtZQUN4QixNQUFNdkUsUUFBUSxJQUFJZjtZQUNsQmUsTUFBTUMsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1lBQ3hCdEIsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQm9CO1lBRWxDLDRDQUE0QztZQUM1QyxNQUFNMEUsZUFBZSxJQUFJekYsS0FBS3VGO1lBQzlCRSxhQUFhcEUsT0FBTyxDQUFDa0UsUUFBUXBGLE9BQU8sS0FBSztZQUN6Q3NGLGFBQWF6RSxRQUFRLENBQUMsR0FBRyxHQUFHLEdBQUc7WUFDL0J0QixRQUFRQyxHQUFHLENBQUMsaUJBQWlCOEY7WUFDN0IvRixRQUFRQyxHQUFHLENBQUMsd0NBQXFDOEYsYUFBYXZFLE9BQU8sS0FBS0gsTUFBTUcsT0FBTztZQUV2Riw4REFBOEQ7WUFDOUQsSUFBSXVFLGFBQWF2RSxPQUFPLEtBQUtILE1BQU1HLE9BQU8sSUFBSTtnQkFDNUN4QixRQUFRQyxHQUFHLENBQUM7Z0JBQ1pELFFBQVFDLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1FBQ0Y7UUFFQUQsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQztRQUNaK0QsZUFBZTZCO0lBQ2pCO0lBRUE7O0dBRUMsR0FDRCxNQUFNRyxrQkFBa0IsQ0FBQy9HO1FBQ3ZCLE1BQU0yRCxVQUFVLElBQUl0QyxLQUFLckI7UUFDekIyRCxRQUFRakIsT0FBTyxDQUFDMUMsVUFBVXdCLE9BQU8sS0FBSztRQUV0QyxNQUFNd0YsV0FBV2hILFVBQVV3QixPQUFPO1FBQ2xDLE1BQU15RixTQUFTdEQsUUFBUW5DLE9BQU87UUFDOUIsTUFBTTBGLFFBQVFsSCxVQUFVbUgsa0JBQWtCLENBQUMsU0FBUztZQUFFRCxPQUFPO1FBQU87UUFDcEUsTUFBTUUsT0FBT3BILFVBQVVzQixXQUFXO1FBRWxDLE9BQU8sR0FBZTJGLE9BQVpELFVBQVMsS0FBYUUsT0FBVkQsUUFBTyxLQUFZRyxPQUFURixPQUFNLEtBQVEsT0FBTEU7SUFDM0M7SUFFQTs7R0FFQyxHQUNELE1BQU1DLG1CQUFtQixDQUFDakc7UUFDeEIsc0RBQXNEO1FBQ3RETCxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0k7UUFFNUMsaURBQWlEO1FBQ2pELDZEQUE2RDtRQUM3RCx1RUFBdUU7UUFDdkUsTUFBTSxDQUFDZ0csTUFBTUYsT0FBT3JCLElBQUksR0FBR3pFLEtBQUtnQyxLQUFLLENBQUMsS0FBS3dDLEdBQUcsQ0FBQzBCO1FBQy9DLE1BQU1DLFVBQVUsSUFBSWxHLEtBQUsrRixNQUFNRixRQUFRLEdBQUdyQixNQUFNLHFCQUFxQjtRQUNyRSxNQUFNNUQsa0JBQWtCLEdBQTRCQyxPQUF6QnFGLFFBQVFqRyxXQUFXLElBQUcsS0FBc0RZLE9BQW5EQSxPQUFPcUYsUUFBUWhHLFFBQVEsS0FBSyxHQUFHWSxRQUFRLENBQUMsR0FBRyxNQUFLLEtBQThDLE9BQTNDRCxPQUFPcUYsUUFBUS9GLE9BQU8sSUFBSVcsUUFBUSxDQUFDLEdBQUc7UUFFN0kseUNBQXlDO1FBQ3pDLE1BQU1xRixhQUFhLElBQUluRyxLQUFLRCxPQUFPLGtEQUFrRDtRQUVyRkwsUUFBUUMsR0FBRyxDQUFDLHVDQUF1Q3VHO1FBQ25EeEcsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q3dHO1FBQ3hEekcsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQndHLFdBQVd2RyxXQUFXO1FBQzVERixRQUFRQyxHQUFHLENBQUMsNkNBQTZDaUI7UUFDekRsQixRQUFRQyxHQUFHLENBQUMsOEJBQThCdUcsUUFBUXJHLGlCQUFpQjtRQUVuRSxxRkFBcUY7UUFDckYsTUFBTXVHLHVCQUF1QkQsV0FBV3ZHLFdBQVcsR0FBR21DLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUNuRSxNQUFNc0UseUJBQXlCSCxRQUFRdEcsV0FBVyxHQUFHbUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ2xFLE1BQU11RSxxQkFBcUJ2RyxTQUFTcUc7UUFFcEMxRyxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQkk7UUFDakNMLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUN5RztRQUNqRDFHLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUMwRztRQUNuRDNHLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NpQjtRQUNwRGxCLFFBQVFDLEdBQUcsQ0FBQyw4QkFBMkIyRztRQUN2QzVHLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBb0NpQixvQkFBb0JiO1FBRXBFTCxRQUFRQyxHQUFHLENBQUMsWUFBWXNEO1FBQ3hCdkQsUUFBUUMsR0FBRyxDQUFDLGtDQUErQkksT0FBT2tEO1FBRWxELHVCQUF1QjtRQUN2QixJQUFJQSxXQUFXbEQsT0FBT2tELFNBQVM7WUFDN0J2RCxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBRCxRQUFRQyxHQUFHLENBQUMsa0RBQWtESTtRQUM5RGlELGFBQWFqRDtRQUNiTCxRQUFRQyxHQUFHLENBQUM7SUFDZDtJQUVBOztHQUVDLEdBQ0QsTUFBTTRHLDJCQUEyQnpJLGtEQUFXQSxDQUFDO1FBQzNDLElBQUksQ0FBQ3FGLDBCQUEwQixDQUFDQyxhQUFhbkUsU0FBU29GLE1BQU0sS0FBSyxHQUFHO1lBQ2xFO1FBQ0Y7UUFFQUwsc0JBQXNCO1FBRXRCLElBQUk7WUFDRixNQUFNd0Msb0JBQW9CLElBQUloSSxrRkFBc0JBLENBQUNJO1lBRXJELHVEQUF1RDtZQUN2RCxNQUFNNkgsbUJBQW1CeEgsU0FDdEJ5SCxNQUFNLENBQUNsQyxDQUFBQSxNQUFPQSxJQUFJNUMsaUJBQWlCLEtBQUssUUFDeEMrRSxPQUFPLENBQUNuQyxDQUFBQTtvQkFDUEE7dUJBQUFBLEVBQUFBLGFBQUFBLElBQUlvQyxLQUFLLGNBQVRwQyxpQ0FBQUEsV0FBV0QsR0FBRyxDQUFDc0MsQ0FBQUEsT0FBUzt3QkFDdEI5RyxNQUFNeUUsSUFBSXpFLElBQUk7d0JBQ2Q0RSxNQUFNa0MsS0FBS2xDLElBQUk7d0JBQ2Y3RixVQUFVK0gsS0FBSy9ILFFBQVE7d0JBQ3ZCZ0ksWUFBWUQsS0FBS0MsVUFBVTt3QkFDM0JsQyxXQUFXaUMsS0FBS2pDLFNBQVM7d0JBQ3pCbUMsT0FBT0YsS0FBS0UsS0FBSztvQkFDbkIsUUFBTztvQkFBQzt3QkFDTmhILE1BQU15RSxJQUFJekUsSUFBSTt3QkFDZDRFLE1BQU07d0JBQ05DLFdBQVc7b0JBQ2I7aUJBQUU7O1lBR04sSUFBSTZCLGlCQUFpQnBDLE1BQU0sS0FBSyxHQUFHO2dCQUNqQ1Asb0JBQW9CO2dCQUNwQjtZQUNGO1lBRUEsTUFBTWtELFNBQVMsTUFBTVIsa0JBQWtCUyxtQkFBbUIsQ0FDeEQ3RCxXQUNBcUQ7WUFHRjNDLG9CQUFvQmtEO1lBQ3BCOUMsd0JBQXdCO1FBQzFCLEVBQUUsT0FBTzdFLE9BQU87WUFDZEssUUFBUUwsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckR5RSxvQkFBb0I7UUFDdEIsU0FBVTtZQUNSRSxzQkFBc0I7UUFDeEI7SUFDRixHQUFHO1FBQUNiO1FBQXdCQztRQUFXbkU7UUFBVUw7S0FBZTtJQUVoRTs7R0FFQyxHQUNELE1BQU1zSSxtQkFBbUI7UUFDdkIsSUFBSSxDQUFDL0QsMEJBQTBCLEVBQUNDLHNCQUFBQSxnQ0FBQUEsVUFBVytELGNBQWMsR0FBRTtZQUN6RCxPQUFPLEVBQUU7UUFDWDtRQUVBLE9BQU9sSSxTQUFTeUgsTUFBTSxDQUFDbEMsQ0FBQUE7Z0JBQ3JCcEI7bUJBQUFBLEVBQUFBLDRCQUFBQSxVQUFVK0QsY0FBYyxjQUF4Qi9ELGdEQUFBQSwwQkFBMEJnRSxRQUFRLENBQUM1QyxJQUFJekUsSUFBSSxNQUMzQ3lFLElBQUk1QyxpQkFBaUIsS0FBSzs7SUFFOUI7SUFFQSxNQUFNeUYsZ0JBQWdCSDtJQUV0Qiw0REFBNEQ7SUFDNURySixnREFBU0EsQ0FBQztRQUNSLElBQUlzRiwwQkFBMEJDLGFBQWFuRSxTQUFTb0YsTUFBTSxHQUFHLEdBQUc7WUFDOURrQztRQUNGO0lBQ0YsR0FBRztRQUFDQTtLQUF5QjtJQUU3Qjs7R0FFQyxHQUNELE1BQU1lLDhCQUE4QixDQUFDQztRQUNuQyxJQUFJQSxXQUFXaEYsSUFBSSxDQUFDeEMsSUFBSSxFQUFFO1lBQ3hCaUcsaUJBQWlCdUIsV0FBV2hGLElBQUksQ0FBQ3hDLElBQUk7UUFDdkM7UUFDQW1FLHdCQUF3QjtJQUMxQjtJQUVBLHFCQUNFLDhEQUFDc0Q7UUFBSWhFLFdBQVcsNERBQXNFLE9BQVZBOzswQkFFMUUsOERBQUNnRTtnQkFBSWhFLFdBQVU7O2tDQUNiLDhEQUFDaUU7d0JBQUdqRSxXQUFVOzswQ0FDWiw4REFBQ3RGLG1JQUFRQTtnQ0FBQ3NGLFdBQVU7Ozs7Ozs0QkFDbkJYOzs7Ozs7O2tDQUVILDhEQUFDNkU7d0JBQUVsRSxXQUFVO2tDQUFpQlY7Ozs7Ozs7Ozs7OztZQUkvQkssMEJBQTBCYyx3QkFBd0JKLGtDQUNqRCw4REFBQzJEO2dCQUFJaEUsV0FBVTswQkFDYiw0RUFBQ2pGLDhFQUF1QkE7b0JBQ3RCb0osbUJBQW1COUQ7b0JBQ25CK0Qsb0JBQW9CTjtvQkFDcEJuSSxTQUFTNEU7b0JBQ1Q4RCxhQUFhO29CQUNiQyxTQUFTeEU7Ozs7Ozs7Ozs7O1lBTWRILDBCQUEwQixDQUFDYyx3QkFBd0JvRCxjQUFjaEQsTUFBTSxHQUFHLG1CQUN6RSw4REFBQ21EO2dCQUFJaEUsV0FBVTs7a0NBQ2IsOERBQUN1RTt3QkFBR3ZFLFdBQVU7OzBDQUNaLDhEQUFDcEYsbUlBQUdBO2dDQUFDb0YsV0FBVTs7Ozs7OzRCQUFpQzs7Ozs7OztrQ0FHbEQsOERBQUNnRTt3QkFBSWhFLFdBQVU7a0NBQ1o2RCxjQUFjVyxLQUFLLENBQUMsR0FBRyxHQUFHekQsR0FBRyxDQUFDLENBQUNnRCxZQUFZVSxzQkFDMUMsOERBQUNDO2dDQUVDQyxNQUFLO2dDQUNMQyxTQUFTLElBQU1wQyxpQkFBaUJ1QixXQUFXeEgsSUFBSTtnQ0FDL0N5RCxXQUFVOztrREFFViw4REFBQ2dFO3dDQUFJaEUsV0FBVTtrREFDWitELFdBQVd0RyxPQUFPLEdBQUcsUUFBUXNHLFdBQVdqRyxVQUFVLEdBQUcsY0FBV2lHLFdBQVc1RyxPQUFPOzs7Ozs7a0RBRXJGLDhEQUFDNkc7d0NBQUloRSxXQUFVO2tEQUF5QitELFdBQVd4SCxJQUFJOzs7Ozs7a0RBQ3ZELDhEQUFDeUg7d0NBQUloRSxXQUFVOzs0Q0FDWnlFLFVBQVUsS0FBSzs0Q0FDZkEsVUFBVSxLQUFLOzRDQUNmQSxVQUFVLEtBQUs7Ozs7Ozs7OytCQVpiVixXQUFXeEgsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztZQXFCN0JvRCwwQkFBMEJZLG9DQUN6Qiw4REFBQ3lEO2dCQUFJaEUsV0FBVTswQkFDYiw0RUFBQ2dFO29CQUFJaEUsV0FBVTs7c0NBQ2IsOERBQUNnRTs0QkFBSWhFLFdBQVU7Ozs7OztzQ0FDZiw4REFBQzZFOzRCQUFLN0UsV0FBVTtzQ0FBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1wRCw4REFBQ2dFO2dCQUFJaEUsV0FBVTs7a0NBQ2IsOERBQUMwRTt3QkFDQ0MsTUFBSzt3QkFDTEMsU0FBUyxJQUFNL0MsYUFBYTt3QkFDNUJpRCxVQUFVbkU7d0JBQ1ZYLFdBQVU7OzBDQUVWLDhEQUFDeEYsbUlBQVdBO2dDQUFDd0YsV0FBVTs7Ozs7OzRCQUFpQjs7Ozs7OztrQ0FJMUMsOERBQUN1RTt3QkFBR3ZFLFdBQVU7a0NBQ1hrQyxnQkFBZ0JqQzs7Ozs7O2tDQUduQiw4REFBQ3lFO3dCQUNDQyxNQUFLO3dCQUNMQyxTQUFTLElBQU0vQyxhQUFhO3dCQUM1QmlELFVBQVVuRTt3QkFDVlgsV0FBVTs7NEJBQ1g7MENBRUMsOERBQUN2RixtSUFBWUE7Z0NBQUN1RixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLM0JXLDJCQUNDLDhEQUFDcUQ7Z0JBQUloRSxXQUFVOztrQ0FDYiw4REFBQ2dFO3dCQUFJaEUsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDNkU7d0JBQUs3RSxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7O1lBS25DbkUsdUJBQ0MsOERBQUNtSTtnQkFBSWhFLFdBQVU7O2tDQUNiLDhEQUFDZ0U7d0JBQUloRSxXQUFVOzswQ0FDYiw4REFBQ25GLG9JQUFXQTtnQ0FBQ21GLFdBQVU7Ozs7OzswQ0FDdkIsOERBQUM2RTtnQ0FBSzdFLFdBQVU7MENBQTJCOzs7Ozs7Ozs7Ozs7a0NBRTdDLDhEQUFDa0U7d0JBQUVsRSxXQUFVO2tDQUE2Qm5FOzs7Ozs7a0NBQzFDLDhEQUFDNkk7d0JBQ0NDLE1BQUs7d0JBQ0xDLFNBQVN6Rjt3QkFDVGEsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7O1lBT0osQ0FBQ1csYUFBYSxDQUFDOUUsU0FBU0osU0FBU29GLE1BQU0sR0FBRyxtQkFDekMsOERBQUNtRDtnQkFBSWhFLFdBQVU7MEJBQ2IsNEVBQUNsRixzRUFBa0JBO29CQUNqQlcsVUFBVUEsU0FBU3NGLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBUTs0QkFDN0J6RSxNQUFNeUUsSUFBSXpFLElBQUk7NEJBQ2RZLFNBQVM2RCxJQUFJN0QsT0FBTzs0QkFDcEJhLFlBQVlnRCxJQUFJaEQsVUFBVTt3QkFDNUI7b0JBQ0F1QixjQUFjQTtvQkFDZEMsY0FBY2dEO29CQUNkdUMsTUFBSzs7Ozs7Ozs7Ozs7WUFNVnJGLHlCQUF5QixDQUFDaUIsMkJBQ3pCLDhEQUFDcUQ7Z0JBQUloRSxXQUFVOztrQ0FDYiw4REFBQ2dGO3dCQUFHaEYsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNnRTt3QkFBSWhFLFdBQVU7OzBDQUNiLDhEQUFDZ0U7Z0NBQUloRSxXQUFVOztrREFDYiw4REFBQ2dFO3dDQUFJaEUsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDNkU7d0NBQUs3RSxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUVsQyw4REFBQ2dFO2dDQUFJaEUsV0FBVTs7a0RBQ2IsOERBQUNnRTt3Q0FBSWhFLFdBQVU7Ozs7OztrREFDZiw4REFBQzZFO3dDQUFLN0UsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FFbEMsOERBQUNnRTtnQ0FBSWhFLFdBQVU7O2tEQUNiLDhEQUFDZ0U7d0NBQUloRSxXQUFVOzs7Ozs7a0RBQ2YsOERBQUM2RTt3Q0FBSzdFLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBRWxDLDhEQUFDZ0U7Z0NBQUloRSxXQUFVOztrREFDYiw4REFBQ2dFO3dDQUFJaEUsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDNkU7d0NBQUs3RSxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT3ZDLENBQUNXLGFBQWEsQ0FBQzlFLFNBQVNKLFNBQVNvRixNQUFNLEtBQUssbUJBQzNDLDhEQUFDbUQ7Z0JBQUloRSxXQUFVOztrQ0FDYiw4REFBQ3JGLG9JQUFLQTt3QkFBQ3FGLFdBQVU7Ozs7OztrQ0FDakIsOERBQUN1RTt3QkFBR3ZFLFdBQVU7a0NBQXlDOzs7Ozs7a0NBQ3ZELDhEQUFDa0U7d0JBQUVsRSxXQUFVO2tDQUFnQjs7Ozs7O2tDQUM3Qiw4REFBQzBFO3dCQUNDQyxNQUFLO3dCQUNMQyxTQUFTLElBQU0vQyxhQUFhO3dCQUM1QjdCLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YO0lBcGVNWjs7UUEwQnVEbEU7OztLQTFCdkRrRTtBQXNlTiwrREFBZUEsMEJBQTBCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2FwcG9pbnRtZW50cy9XZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3Rvci50c3g/ZDFhNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbi8qKlxuICogV2Vla2x5QXZhaWxhYmlsaXR5U2VsZWN0b3IgQ29tcG9uZW50XG4gKiBcbiAqIENvbXBvbmVudGUgYXZhbnphZG8gcGFyYSBzZWxlY2Npw7NuIGRlIGZlY2hhcyBjb24gdmlzdGEgc2VtYW5hbCxcbiAqIGluZGljYWRvcmVzIGRlIGRlbnNpZGFkIGRlIGRpc3BvbmliaWxpZGFkIHkgbmF2ZWdhY2nDs24gaW50dWl0aXZhLlxuICogUmVlbXBsYXphIGFsIERhdGVTZWxlY3RvciB0cmFkaWNpb25hbCBlbiBVbmlmaWVkQXBwb2ludG1lbnRGbG93LlxuICogXG4gKiBDYXJhY3RlcsOtc3RpY2FzIHByaW5jaXBhbGVzOlxuICogLSBWaXN0YSBzZW1hbmFsIGNvbiBpbmRpY2Fkb3JlcyBkZSBkZW5zaWRhZCB2aXN1YWxcbiAqIC0gTmF2ZWdhY2nDs24gZmx1aWRhIGVudHJlIHNlbWFuYXNcbiAqIC0gQ29sb3JlcyBzZW1hZsOzcmljb3MgcGFyYSBkaXNwb25pYmlsaWRhZCAodmVyZGUvYW1hcmlsbG8vcm9qby9ncmlzKVxuICogLSBJbnRlZ3JhY2nDs24gY29uIHN1Z2VyZW5jaWFzIGRlIElBXG4gKiAtIFNvcG9ydGUgcGFyYSBmbHVqb3MgRXhwcmVzcyB5IFBlcnNvbmFsaXplZFxuICogLSBBcnF1aXRlY3R1cmEgbXVsdGktdGVuYW50XG4gKiBcbiAqIEBhdXRob3IgQWdlbnRTYWx1ZCBNVlAgVGVhbSAtIFVYIEVuaGFuY2VtZW50IFBoYXNlIDFcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKi9cblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ2hldnJvbkxlZnQsIENoZXZyb25SaWdodCwgQ2FsZW5kYXIsIENsb2NrLCBaYXAsIEFsZXJ0Q2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCBBdmFpbGFiaWxpdHlJbmRpY2F0b3IsIHsgV2Vla2x5QXZhaWxhYmlsaXR5IH0gZnJvbSAnLi9BdmFpbGFiaWxpdHlJbmRpY2F0b3InO1xuaW1wb3J0IFNtYXJ0U3VnZ2VzdGlvbnNEaXNwbGF5IGZyb20gJ0AvY29tcG9uZW50cy9haS9TbWFydFN1Z2dlc3Rpb25zRGlzcGxheSc7XG5pbXBvcnQgeyBTbWFydFN1Z2dlc3Rpb25zRW5naW5lLCB0eXBlIFNtYXJ0U3VnZ2VzdGlvbiwgdHlwZSBTdWdnZXN0aW9uc1Jlc3VsdCB9IGZyb20gJ0AvbGliL2FpL1NtYXJ0U3VnZ2VzdGlvbnNFbmdpbmUnO1xuaW1wb3J0IHsgdmFsaWRhdGVNdWx0aXBsZURhdGVzLCB0eXBlIERhdGVWYWxpZGF0aW9uUmVzdWx0IH0gZnJvbSAnQC9saWIvdXRpbHMvZGF0ZVZhbGlkYXRpb24nO1xuXG4vKipcbiAqIFRpcG9zIGRlIGRpc3BvbmliaWxpZGFkIHBhcmEgaW5kaWNhZG9yZXMgdmlzdWFsZXNcbiAqL1xuZXhwb3J0IHR5cGUgQXZhaWxhYmlsaXR5TGV2ZWwgPSAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnIHwgJ25vbmUnO1xuXG4vKipcbiAqIERhdG9zIGRlIGRpc3BvbmliaWxpZGFkIHBvciBkw61hXG4gKi9cbmludGVyZmFjZSBEYXlBdmFpbGFiaWxpdHlEYXRhIHtcbiAgZGF0ZTogc3RyaW5nO1xuICBkYXlOYW1lOiBzdHJpbmc7XG4gIHNsb3RzQ291bnQ6IG51bWJlcjtcbiAgYXZhaWxhYmlsaXR5TGV2ZWw6IEF2YWlsYWJpbGl0eUxldmVsO1xuICBpc1RvZGF5PzogYm9vbGVhbjtcbiAgaXNUb21vcnJvdz86IGJvb2xlYW47XG4gIGlzV2Vla2VuZD86IGJvb2xlYW47XG59XG5cbi8qKlxuICogQ29udGV4dG8gZGUgSUEgcGFyYSBwcmUtY2FyZ2FyIHN1Z2VyZW5jaWFzXG4gKi9cbmludGVyZmFjZSBBSUNvbnRleHQge1xuICBzdWdnZXN0ZWREYXRlcz86IHN0cmluZ1tdO1xuICBwcmVmZXJyZWRUaW1lUmFuZ2U/OiAnbW9ybmluZycgfCAnYWZ0ZXJub29uJyB8ICdldmVuaW5nJztcbiAgdXJnZW5jeUxldmVsPzogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcbiAgZmxleGliaWxpdHlMZXZlbD86ICdyaWdpZCcgfCAnZmxleGlibGUnIHwgJ3ZlcnktZmxleGlibGUnO1xuICBleHRyYWN0ZWRQcmVmZXJlbmNlcz86IHtcbiAgICBwcmVmZXJyZWREYXlzPzogc3RyaW5nW107XG4gICAgYXZvaWRlZERheXM/OiBzdHJpbmdbXTtcbiAgICB0aW1lUHJlZmVyZW5jZXM/OiBzdHJpbmdbXTtcbiAgfTtcbn1cblxuLyoqXG4gKiBQcm9wcyBkZWwgY29tcG9uZW50ZSBXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvclxuICovXG5pbnRlcmZhY2UgV2Vla2x5QXZhaWxhYmlsaXR5U2VsZWN0b3JQcm9wcyB7XG4gIC8qKiBUw610dWxvIGRlbCBzZWxlY3RvciAqL1xuICB0aXRsZTogc3RyaW5nO1xuICAvKiogU3VidMOtdHVsbyBkZXNjcmlwdGl2byAqL1xuICBzdWJ0aXRsZTogc3RyaW5nO1xuICAvKiogRmVjaGEgc2VsZWNjaW9uYWRhIGFjdHVhbG1lbnRlICovXG4gIHNlbGVjdGVkRGF0ZT86IHN0cmluZztcbiAgLyoqIENhbGxiYWNrIGN1YW5kbyBzZSBzZWxlY2Npb25hIHVuYSBmZWNoYSAqL1xuICBvbkRhdGVTZWxlY3Q6IChkYXRlOiBzdHJpbmcpID0+IHZvaWQ7XG4gIC8qKiBJRCBkZSBsYSBvcmdhbml6YWNpw7NuIChtdWx0aS10ZW5hbnQpICovXG4gIG9yZ2FuaXphdGlvbklkOiBzdHJpbmc7XG4gIC8qKiBJRCBkZWwgc2VydmljaW8gc2VsZWNjaW9uYWRvICovXG4gIHNlcnZpY2VJZD86IHN0cmluZztcbiAgLyoqIElEIGRlbCBkb2N0b3Igc2VsZWNjaW9uYWRvIChvcGNpb25hbCkgKi9cbiAgZG9jdG9ySWQ/OiBzdHJpbmc7XG4gIC8qKiBJRCBkZSBsYSB1YmljYWNpw7NuIHNlbGVjY2lvbmFkYSAob3BjaW9uYWwpICovXG4gIGxvY2F0aW9uSWQ/OiBzdHJpbmc7XG4gIC8qKiBGZWNoYSBtw61uaW1hIHNlbGVjY2lvbmFibGUgKi9cbiAgbWluRGF0ZT86IHN0cmluZztcbiAgLyoqIE1vc3RyYXIgaW5kaWNhZG9yZXMgZGUgZGVuc2lkYWQgKi9cbiAgc2hvd0RlbnNpdHlJbmRpY2F0b3JzPzogYm9vbGVhbjtcbiAgLyoqIEhhYmlsaXRhciBzdWdlcmVuY2lhcyBpbnRlbGlnZW50ZXMgKi9cbiAgZW5hYmxlU21hcnRTdWdnZXN0aW9ucz86IGJvb2xlYW47XG4gIC8qKiBDb250ZXh0byBkZSBJQSBwYXJhIHN1Z2VyZW5jaWFzICovXG4gIGFpQ29udGV4dD86IEFJQ29udGV4dDtcbiAgLyoqIE1vZG8gZGUgZW50cmFkYSAoQUkgdnMgbWFudWFsKSAqL1xuICBlbnRyeU1vZGU/OiAnYWknIHwgJ21hbnVhbCc7XG4gIC8qKiBVc2FyIHN1Z2VyZW5jaWFzIGNvbXBhY3RhcyAqL1xuICBjb21wYWN0U3VnZ2VzdGlvbnM/OiBib29sZWFuO1xuICAvKiogQ2FsbGJhY2sgcGFyYSBjYXJnYXIgZGlzcG9uaWJpbGlkYWQgKi9cbiAgb25Mb2FkQXZhaWxhYmlsaXR5PzogKHBhcmFtczoge1xuICAgIG9yZ2FuaXphdGlvbklkOiBzdHJpbmc7XG4gICAgc2VydmljZUlkPzogc3RyaW5nO1xuICAgIGRvY3RvcklkPzogc3RyaW5nO1xuICAgIGxvY2F0aW9uSWQ/OiBzdHJpbmc7XG4gICAgc3RhcnREYXRlOiBzdHJpbmc7XG4gICAgZW5kRGF0ZTogc3RyaW5nO1xuICB9KSA9PiBQcm9taXNlPERheUF2YWlsYWJpbGl0eURhdGFbXT47XG4gIC8qKiBFc3RhZG8gZGUgY2FyZ2EgKi9cbiAgbG9hZGluZz86IGJvb2xlYW47XG4gIC8qKiBDbGFzZSBDU1MgYWRpY2lvbmFsICovXG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuLyoqXG4gKiBIb29rIHBhcmEgZ2VuZXJhciBkYXRvcyBkZSBkaXNwb25pYmlsaWRhZCBzZW1hbmFsXG4gKi9cbmNvbnN0IHVzZVdlZWtseUF2YWlsYWJpbGl0eURhdGEgPSAoXG4gIHN0YXJ0RGF0ZTogRGF0ZSxcbiAgb3JnYW5pemF0aW9uSWQ6IHN0cmluZyxcbiAgc2VydmljZUlkPzogc3RyaW5nLFxuICBkb2N0b3JJZD86IHN0cmluZyxcbiAgbG9jYXRpb25JZD86IHN0cmluZyxcbiAgb25Mb2FkQXZhaWxhYmlsaXR5PzogV2Vla2x5QXZhaWxhYmlsaXR5U2VsZWN0b3JQcm9wc1snb25Mb2FkQXZhaWxhYmlsaXR5J11cbikgPT4ge1xuICBjb25zdCBbd2Vla0RhdGEsIHNldFdlZWtEYXRhXSA9IHVzZVN0YXRlPERheUF2YWlsYWJpbGl0eURhdGFbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgbG9hZFdlZWtEYXRhID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghb25Mb2FkQXZhaWxhYmlsaXR5KSB7XG4gICAgICAvLyBHZW5lcmFyIGRhdG9zIGRlIGVqZW1wbG8gc2kgbm8gaGF5IGZ1bmNpw7NuIGRlIGNhcmdhXG4gICAgICBjb25zdCBtb2NrRGF0YTogRGF5QXZhaWxhYmlsaXR5RGF0YVtdID0gW107XG4gICAgICBjb25zdCBkYXlOYW1lcyA9IFsnRG9taW5nbycsICdMdW5lcycsICdNYXJ0ZXMnLCAnTWnDqXJjb2xlcycsICdKdWV2ZXMnLCAnVmllcm5lcycsICdTw6FiYWRvJ107XG4gICAgICBcbiAgICAgIC8vIERFQlVHOiBMb2cgaW5pY2lhbCBwYXJhIGludmVzdGlnYXIgcHJvYmxlbWEgZGUgZmVjaGFzXG4gICAgICBjb25zb2xlLmxvZygnPT09IERFQlVHIEZFQ0hBIEdFTkVSQUNJw5NOID09PScpO1xuICAgICAgY29uc29sZS5sb2coJ3N0YXJ0RGF0ZSBvcmlnaW5hbDonLCBzdGFydERhdGUpO1xuICAgICAgY29uc29sZS5sb2coJ3N0YXJ0RGF0ZSBJU086Jywgc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkpO1xuICAgICAgY29uc29sZS5sb2coJ3N0YXJ0RGF0ZSB0aW1lem9uZSBvZmZzZXQ6Jywgc3RhcnREYXRlLmdldFRpbWV6b25lT2Zmc2V0KCkpO1xuXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDc7IGkrKykge1xuICAgICAgICAvLyBDUklUSUNBTCBGSVg6IFVzZSB0aW1lem9uZS1zYWZlIGRhdGUgY2FsY3VsYXRpb25cbiAgICAgICAgLy8gSW5zdGVhZCBvZiBzZXREYXRlKCkgd2hpY2ggY2FuIGNhdXNlIHRpbWV6b25lIGlzc3VlcywgdXNlIGRpcmVjdCBkYXRlIGFyaXRobWV0aWNcbiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHN0YXJ0RGF0ZS5nZXRGdWxsWWVhcigpLCBzdGFydERhdGUuZ2V0TW9udGgoKSwgc3RhcnREYXRlLmdldERhdGUoKSArIGkpO1xuXG4gICAgICAgIC8vIERFQlVHOiBMb2cgYW50ZXMgZGUgY8OhbGN1bG9cbiAgICAgICAgY29uc29sZS5sb2coYETDrWEgJHtpfSAoYW50ZXMgY8OhbGN1bG8pOmAsIHtcbiAgICAgICAgICBzdGFydERhdGVZZWFyOiBzdGFydERhdGUuZ2V0RnVsbFllYXIoKSxcbiAgICAgICAgICBzdGFydERhdGVNb250aDogc3RhcnREYXRlLmdldE1vbnRoKCksXG4gICAgICAgICAgc3RhcnREYXRlRGF0ZTogc3RhcnREYXRlLmdldERhdGUoKSxcbiAgICAgICAgICBpbmRleEk6IGksXG4gICAgICAgICAgY2FsY3VsYXRpb246IHN0YXJ0RGF0ZS5nZXREYXRlKCkgKyBpXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIERFQlVHOiBMb2cgZGVzcHXDqXMgZGUgY8OhbGN1bG8gdGltZXpvbmUtc2FmZVxuICAgICAgICBjb25zb2xlLmxvZyhgRMOtYSAke2l9IChkZXNwdcOpcyBjw6FsY3VsbyB0aW1lem9uZS1zYWZlKTpgLCB7XG4gICAgICAgICAgbmV3RGF0ZTogZGF0ZS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIGdldERhdGU6IGRhdGUuZ2V0RGF0ZSgpLFxuICAgICAgICAgIGdldERheTogZGF0ZS5nZXREYXkoKSxcbiAgICAgICAgICBkYXlOYW1lOiBkYXlOYW1lc1tkYXRlLmdldERheSgpXSxcbiAgICAgICAgICBsb2NhbERhdGVTdHJpbmc6IGAke2RhdGUuZ2V0RnVsbFllYXIoKX0tJHtTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKX0tJHtTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyl9YFxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7XG4gICAgICAgIHRvZGF5LnNldEhvdXJzKDAsIDAsIDAsIDApOyAvLyBOb3JtYWxpemFyIGEgbWVkaWFub2NoZSBwYXJhIGNvbXBhcmFjacOzblxuICAgICAgICBkYXRlLnNldEhvdXJzKDAsIDAsIDAsIDApOyAvLyBOb3JtYWxpemFyIGEgbWVkaWFub2NoZSBwYXJhIGNvbXBhcmFjacOzblxuXG4gICAgICAgIGNvbnN0IGlzVG9kYXkgPSBkYXRlLmdldFRpbWUoKSA9PT0gdG9kYXkuZ2V0VGltZSgpO1xuICAgICAgICBjb25zdCBpc1Bhc3REYXRlID0gZGF0ZS5nZXRUaW1lKCkgPCB0b2RheS5nZXRUaW1lKCk7XG4gICAgICAgIGNvbnN0IHRvbW9ycm93ID0gbmV3IERhdGUodG9kYXkpO1xuICAgICAgICB0b21vcnJvdy5zZXREYXRlKHRvZGF5LmdldERhdGUoKSArIDEpO1xuICAgICAgICBjb25zdCBpc1RvbW9ycm93ID0gZGF0ZS5nZXRUaW1lKCkgPT09IHRvbW9ycm93LmdldFRpbWUoKTtcblxuICAgICAgICBjb25zdCBpc1dlZWtlbmQgPSBkYXRlLmdldERheSgpID09PSAwIHx8IGRhdGUuZ2V0RGF5KCkgPT09IDY7XG5cbiAgICAgICAgLy8gU2kgZXMgZmVjaGEgcGFzYWRhLCBubyBtb3N0cmFyIHNsb3RzIGRpc3BvbmlibGVzXG4gICAgICAgIGxldCBzbG90c0NvdW50ID0gMDtcbiAgICAgICAgaWYgKCFpc1Bhc3REYXRlKSB7XG4gICAgICAgICAgc2xvdHNDb3VudCA9IGlzV2Vla2VuZCA/IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDMpIDogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTApO1xuICAgICAgICB9XG5cbiAgICAgICAgbGV0IGF2YWlsYWJpbGl0eUxldmVsOiBBdmFpbGFiaWxpdHlMZXZlbCA9ICdub25lJztcbiAgICAgICAgaWYgKGlzUGFzdERhdGUpIHtcbiAgICAgICAgICBhdmFpbGFiaWxpdHlMZXZlbCA9ICdub25lJzsgLy8gRmVjaGFzIHBhc2FkYXMgc2llbXByZSBzaW4gZGlzcG9uaWJpbGlkYWRcbiAgICAgICAgfSBlbHNlIGlmIChzbG90c0NvdW50ID09PSAwKSB7XG4gICAgICAgICAgYXZhaWxhYmlsaXR5TGV2ZWwgPSAnbm9uZSc7XG4gICAgICAgIH0gZWxzZSBpZiAoc2xvdHNDb3VudCA8PSAyKSB7XG4gICAgICAgICAgYXZhaWxhYmlsaXR5TGV2ZWwgPSAnbG93JztcbiAgICAgICAgfSBlbHNlIGlmIChzbG90c0NvdW50IDw9IDUpIHtcbiAgICAgICAgICBhdmFpbGFiaWxpdHlMZXZlbCA9ICdtZWRpdW0nO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGF2YWlsYWJpbGl0eUxldmVsID0gJ2hpZ2gnO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ1JJVElDQUwgRklYOiBVc2UgdGltZXpvbmUtc2FmZSBkYXRlIGZvcm1hdHRpbmdcbiAgICAgICAgY29uc3QgZmluYWxEYXRlU3RyaW5nID0gYCR7ZGF0ZS5nZXRGdWxsWWVhcigpfS0ke1N0cmluZyhkYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpfS0ke1N0cmluZyhkYXRlLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKX1gO1xuXG4gICAgICAgIC8vIERFQlVHOiBMb2cgZGF0b3MgZmluYWxlcyBjb24gY29tcGFyYWNpw7NuXG4gICAgICAgIGNvbnNvbGUubG9nKGBEw61hICR7aX0gKGRhdG9zIGZpbmFsZXMpOmAsIHtcbiAgICAgICAgICBkYXRlOiBmaW5hbERhdGVTdHJpbmcsXG4gICAgICAgICAgZGF0ZUlTTzogZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgICAgZGF0ZUxvY2FsOiBmaW5hbERhdGVTdHJpbmcsXG4gICAgICAgICAgZGF5TmFtZTogZGF5TmFtZXNbZGF0ZS5nZXREYXkoKV0sXG4gICAgICAgICAgc2xvdHNDb3VudCxcbiAgICAgICAgICBhdmFpbGFiaWxpdHlMZXZlbCxcbiAgICAgICAgICBpc1RvZGF5LFxuICAgICAgICAgIGlzVG9tb3Jyb3csXG4gICAgICAgICAgaXNXZWVrZW5kLFxuICAgICAgICAgIHRpbWV6b25lQ29tcGFyaXNvbjoge1xuICAgICAgICAgICAgaXNvOiBkYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgICAgIGxvY2FsOiBmaW5hbERhdGVTdHJpbmcsXG4gICAgICAgICAgICBtYXRjaDogZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0gPT09IGZpbmFsRGF0ZVN0cmluZ1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgbW9ja0RhdGEucHVzaCh7XG4gICAgICAgICAgZGF0ZTogZmluYWxEYXRlU3RyaW5nLCAvLyBVc2UgdGltZXpvbmUtc2FmZSBmb3JtYXR0aW5nXG4gICAgICAgICAgZGF5TmFtZTogZGF5TmFtZXNbZGF0ZS5nZXREYXkoKV0sXG4gICAgICAgICAgc2xvdHNDb3VudCxcbiAgICAgICAgICBhdmFpbGFiaWxpdHlMZXZlbCxcbiAgICAgICAgICBpc1RvZGF5LFxuICAgICAgICAgIGlzVG9tb3Jyb3csXG4gICAgICAgICAgaXNXZWVrZW5kXG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnPT09IERFQlVHIE1PQ0sgREFUQSBGSU5BTCA9PT0nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdtb2NrRGF0YSBjb21wbGV0bzonLCBtb2NrRGF0YSk7XG4gICAgICBjb25zb2xlLmxvZygnPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0nKTtcbiAgICAgIFxuICAgICAgc2V0V2Vla0RhdGEobW9ja0RhdGEpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZShzdGFydERhdGUpO1xuICAgICAgZW5kRGF0ZS5zZXREYXRlKHN0YXJ0RGF0ZS5nZXREYXRlKCkgKyA2KTtcbiAgICAgIFxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IG9uTG9hZEF2YWlsYWJpbGl0eSh7XG4gICAgICAgIG9yZ2FuaXphdGlvbklkLFxuICAgICAgICBzZXJ2aWNlSWQsXG4gICAgICAgIGRvY3RvcklkLFxuICAgICAgICBsb2NhdGlvbklkLFxuICAgICAgICBzdGFydERhdGU6IHN0YXJ0RGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgIGVuZERhdGU6IGVuZERhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgc2V0V2Vla0RhdGEoZGF0YSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0Vycm9yIGNhcmdhbmRvIGRpc3BvbmliaWxpZGFkJyk7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGF2YWlsYWJpbGl0eSBkYXRhOicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3N0YXJ0RGF0ZSwgb3JnYW5pemF0aW9uSWQsIHNlcnZpY2VJZCwgZG9jdG9ySWQsIGxvY2F0aW9uSWQsIG9uTG9hZEF2YWlsYWJpbGl0eV0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFdlZWtEYXRhKCk7XG4gIH0sIFtsb2FkV2Vla0RhdGFdKTtcblxuICByZXR1cm4geyB3ZWVrRGF0YSwgbG9hZGluZywgZXJyb3IsIHJlZmV0Y2g6IGxvYWRXZWVrRGF0YSB9O1xufTtcblxuLyoqXG4gKiBDb21wb25lbnRlIHByaW5jaXBhbCBXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvclxuICovXG5jb25zdCBXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvcjogUmVhY3QuRkM8V2Vla2x5QXZhaWxhYmlsaXR5U2VsZWN0b3JQcm9wcz4gPSAoe1xuICB0aXRsZSxcbiAgc3VidGl0bGUsXG4gIHNlbGVjdGVkRGF0ZSxcbiAgb25EYXRlU2VsZWN0LFxuICBvcmdhbml6YXRpb25JZCxcbiAgc2VydmljZUlkLFxuICBkb2N0b3JJZCxcbiAgbG9jYXRpb25JZCxcbiAgbWluRGF0ZSxcbiAgc2hvd0RlbnNpdHlJbmRpY2F0b3JzID0gdHJ1ZSxcbiAgZW5hYmxlU21hcnRTdWdnZXN0aW9ucyA9IGZhbHNlLFxuICBhaUNvbnRleHQsXG4gIGVudHJ5TW9kZSA9ICdtYW51YWwnLFxuICBjb21wYWN0U3VnZ2VzdGlvbnMgPSBmYWxzZSxcbiAgb25Mb2FkQXZhaWxhYmlsaXR5LFxuICBsb2FkaW5nOiBleHRlcm5hbExvYWRpbmcgPSBmYWxzZSxcbiAgY2xhc3NOYW1lID0gJydcbn0pID0+IHtcbiAgY29uc3QgW2N1cnJlbnRXZWVrLCBzZXRDdXJyZW50V2Vla10gPSB1c2VTdGF0ZSgoKSA9PiB7XG4gICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IHN0YXJ0T2ZXZWVrID0gbmV3IERhdGUodG9kYXkpO1xuICAgIHN0YXJ0T2ZXZWVrLnNldERhdGUodG9kYXkuZ2V0RGF0ZSgpIC0gdG9kYXkuZ2V0RGF5KCkpOyAvLyBEb21pbmdvIGNvbW8gaW5pY2lvIGRlIHNlbWFuYVxuICAgIHJldHVybiBzdGFydE9mV2VlaztcbiAgfSk7XG5cbiAgY29uc3QgeyB3ZWVrRGF0YSwgbG9hZGluZzogZGF0YUxvYWRpbmcsIGVycm9yLCByZWZldGNoIH0gPSB1c2VXZWVrbHlBdmFpbGFiaWxpdHlEYXRhKFxuICAgIGN1cnJlbnRXZWVrLFxuICAgIG9yZ2FuaXphdGlvbklkLFxuICAgIHNlcnZpY2VJZCxcbiAgICBkb2N0b3JJZCxcbiAgICBsb2NhdGlvbklkLFxuICAgIG9uTG9hZEF2YWlsYWJpbGl0eVxuICApO1xuXG4gIC8vIFNtYXJ0IFN1Z2dlc3Rpb25zIHN0YXRlXG4gIGNvbnN0IFtzbWFydFN1Z2dlc3Rpb25zLCBzZXRTbWFydFN1Z2dlc3Rpb25zXSA9IHVzZVN0YXRlPFN1Z2dlc3Rpb25zUmVzdWx0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nU3VnZ2VzdGlvbnMsIHNldExvYWRpbmdTdWdnZXN0aW9uc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93U21hcnRTdWdnZXN0aW9ucywgc2V0U2hvd1NtYXJ0U3VnZ2VzdGlvbnNdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGlzTG9hZGluZyA9IGV4dGVybmFsTG9hZGluZyB8fCBkYXRhTG9hZGluZztcblxuICAvKipcbiAgICogQ1JJVElDQUwgRkVBVFVSRTogVUktbGV2ZWwgZGF0ZSBibG9ja2luZyB2YWxpZGF0aW9uXG4gICAqIFZhbGlkYXRlcyB3aGljaCBkYXRlcyBzaG91bGQgYmUgYmxvY2tlZCBiYXNlZCBvbiA0LWhvdXIgYWR2YW5jZSBib29raW5nIHJ1bGVcbiAgICogUHJldmVudHMgdXNlciBjb25mdXNpb24gYnkgc2hvd2luZyBibG9ja2VkIGRhdGVzIGFzIGRpc2FibGVkXG4gICAqL1xuICBjb25zdCBkYXRlVmFsaWRhdGlvblJlc3VsdHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAod2Vla0RhdGEubGVuZ3RoID09PSAwKSByZXR1cm4ge307XG5cbiAgICBjb25zdCBkYXRlcyA9IHdlZWtEYXRhLm1hcChkYXkgPT4gZGF5LmRhdGUpO1xuXG4gICAgLy8gQ3JlYXRlIG1vY2sgdGltZSBzbG90cyBmb3IgdmFsaWRhdGlvbiAodHlwaWNhbCBidXNpbmVzcyBob3VycylcbiAgICBjb25zdCBhdmFpbGFibGVTbG90c0J5RGF0ZTogUmVjb3JkPHN0cmluZywgYW55W10+ID0ge307XG4gICAgd2Vla0RhdGEuZm9yRWFjaChkYXkgPT4ge1xuICAgICAgaWYgKGRheS5hdmFpbGFiaWxpdHlMZXZlbCAhPT0gJ25vbmUnKSB7XG4gICAgICAgIC8vIEdlbmVyYXRlIHR5cGljYWwgYnVzaW5lc3MgaG91cnMgZm9yIHZhbGlkYXRpb25cbiAgICAgICAgYXZhaWxhYmxlU2xvdHNCeURhdGVbZGF5LmRhdGVdID0gW1xuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcwODowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcwOTowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxMDowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxMTowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxNDowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxNTowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxNjowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxNzowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9LFxuICAgICAgICAgIHsgZGF0ZTogZGF5LmRhdGUsIHRpbWU6ICcxODowMCcsIGF2YWlsYWJsZTogdHJ1ZSB9XG4gICAgICAgIF07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhdmFpbGFibGVTbG90c0J5RGF0ZVtkYXkuZGF0ZV0gPSBbXTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUcgREFURSBCTE9DS0lORyBWQUxJREFUSU9OID09PScpO1xuICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0aW5nIGRhdGVzOicsIGRhdGVzKTtcbiAgICBjb25zb2xlLmxvZygnQXZhaWxhYmxlIHNsb3RzIGJ5IGRhdGU6JywgYXZhaWxhYmxlU2xvdHNCeURhdGUpO1xuXG4gICAgY29uc3QgdmFsaWRhdGlvblJlc3VsdHMgPSB2YWxpZGF0ZU11bHRpcGxlRGF0ZXMoZGF0ZXMsIGF2YWlsYWJsZVNsb3RzQnlEYXRlKTtcblxuICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0aW9uIHJlc3VsdHM6JywgdmFsaWRhdGlvblJlc3VsdHMpO1xuICAgIGNvbnNvbGUubG9nKCc9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09Jyk7XG5cbiAgICByZXR1cm4gdmFsaWRhdGlvblJlc3VsdHM7XG4gIH0sIFt3ZWVrRGF0YV0pO1xuXG4gIC8qKlxuICAgKiBFbmhhbmNlZCB3ZWVrIGRhdGEgd2l0aCBibG9ja2luZyBpbmZvcm1hdGlvblxuICAgKi9cbiAgY29uc3QgZW5oYW5jZWRXZWVrRGF0YSA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIHJldHVybiB3ZWVrRGF0YS5tYXAoZGF5ID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkYXRpb24gPSBkYXRlVmFsaWRhdGlvblJlc3VsdHNbZGF5LmRhdGVdO1xuICAgICAgY29uc3QgaXNCbG9ja2VkID0gdmFsaWRhdGlvbiAmJiAhdmFsaWRhdGlvbi5pc1ZhbGlkO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5kYXksXG4gICAgICAgIGlzQmxvY2tlZCxcbiAgICAgICAgYmxvY2tSZWFzb246IHZhbGlkYXRpb24/LnJlYXNvbixcbiAgICAgICAgdmFsaWRhdGlvblJlc3VsdDogdmFsaWRhdGlvblxuICAgICAgfTtcbiAgICB9KTtcbiAgfSwgW3dlZWtEYXRhLCBkYXRlVmFsaWRhdGlvblJlc3VsdHNdKTtcblxuICAvKipcbiAgICogTmF2ZWdhciBlbnRyZSBzZW1hbmFzXG4gICAqL1xuICBjb25zdCBuYXZpZ2F0ZVdlZWsgPSAoZGlyZWN0aW9uOiAncHJldicgfCAnbmV4dCcpID0+IHtcbiAgICAvLyBERUJVRzogTG9nIG5hdmVnYWNpw7NuIHNlbWFuYWxcbiAgICBjb25zb2xlLmxvZygnPT09IERFQlVHIE5BVkVHQUNJw5NOIFNFTUFOQUwgPT09Jyk7XG4gICAgY29uc29sZS5sb2coJ0RpcmVjY2nDs246JywgZGlyZWN0aW9uKTtcbiAgICBjb25zb2xlLmxvZygnY3VycmVudFdlZWsgYWN0dWFsOicsIGN1cnJlbnRXZWVrKTtcbiAgICBjb25zb2xlLmxvZygnY3VycmVudFdlZWsgSVNPOicsIGN1cnJlbnRXZWVrLnRvSVNPU3RyaW5nKCkpO1xuICAgIGNvbnNvbGUubG9nKCdtaW5EYXRlIHByb3A6JywgbWluRGF0ZSk7XG5cbiAgICBjb25zdCBuZXdXZWVrID0gbmV3IERhdGUoY3VycmVudFdlZWspO1xuICAgIG5ld1dlZWsuc2V0RGF0ZShjdXJyZW50V2Vlay5nZXREYXRlKCkgKyAoZGlyZWN0aW9uID09PSAnbmV4dCcgPyA3IDogLTcpKTtcblxuICAgIGNvbnNvbGUubG9nKCduZXdXZWVrIGNhbGN1bGFkYTonLCBuZXdXZWVrKTtcbiAgICBjb25zb2xlLmxvZygnbmV3V2VlayBJU086JywgbmV3V2Vlay50b0lTT1N0cmluZygpKTtcblxuICAgIC8vIFZhbGlkYXIgZmVjaGEgbcOtbmltYVxuICAgIGlmIChtaW5EYXRlICYmIGRpcmVjdGlvbiA9PT0gJ3ByZXYnKSB7XG4gICAgICBjb25zdCBtaW5EYXRlT2JqID0gbmV3IERhdGUobWluRGF0ZSk7XG4gICAgICBjb25zb2xlLmxvZygnbWluRGF0ZU9iajonLCBtaW5EYXRlT2JqKTtcbiAgICAgIGNvbnNvbGUubG9nKCdDb21wYXJhY2nDs24gbmV3V2VlayA8IG1pbkRhdGVPYmo6JywgbmV3V2VlayA8IG1pbkRhdGVPYmopO1xuXG4gICAgICBpZiAobmV3V2VlayA8IG1pbkRhdGVPYmopIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0JMT1FVRUFETyBwb3IgbWluRGF0ZSAtIG5vIHNlIHBlcm1pdGUgbmF2ZWdhciBhbnRlcyBkZSBmZWNoYSBtw61uaW1hJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCc9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PScpO1xuICAgICAgICByZXR1cm47IC8vIE5vIHBlcm1pdGlyIG5hdmVnYXIgYW50ZXMgZGUgbGEgZmVjaGEgbcOtbmltYVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXIgcXVlIG5vIHNlIG5hdmVndWUgYSBzZW1hbmFzIGNvbXBsZXRhbWVudGUgZW4gZWwgcGFzYWRvXG4gICAgaWYgKGRpcmVjdGlvbiA9PT0gJ3ByZXYnKSB7XG4gICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7XG4gICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTtcbiAgICAgIGNvbnNvbGUubG9nKCd0b2RheSBub3JtYWxpemFkbzonLCB0b2RheSk7XG5cbiAgICAgIC8vIENhbGN1bGFyIGVsIMO6bHRpbW8gZMOtYSBkZSBsYSBudWV2YSBzZW1hbmFcbiAgICAgIGNvbnN0IGVuZE9mTmV3V2VlayA9IG5ldyBEYXRlKG5ld1dlZWspO1xuICAgICAgZW5kT2ZOZXdXZWVrLnNldERhdGUobmV3V2Vlay5nZXREYXRlKCkgKyA2KTtcbiAgICAgIGVuZE9mTmV3V2Vlay5zZXRIb3VycygwLCAwLCAwLCAwKTtcbiAgICAgIGNvbnNvbGUubG9nKCdlbmRPZk5ld1dlZWs6JywgZW5kT2ZOZXdXZWVrKTtcbiAgICAgIGNvbnNvbGUubG9nKCdDb21wYXJhY2nDs24gZW5kT2ZOZXdXZWVrIDwgdG9kYXk6JywgZW5kT2ZOZXdXZWVrLmdldFRpbWUoKSA8IHRvZGF5LmdldFRpbWUoKSk7XG5cbiAgICAgIC8vIFNpIHRvZGEgbGEgc2VtYW5hIGVzdMOhIGVuIGVsIHBhc2Fkbywgbm8gcGVybWl0aXIgbmF2ZWdhY2nDs25cbiAgICAgIGlmIChlbmRPZk5ld1dlZWsuZ2V0VGltZSgpIDwgdG9kYXkuZ2V0VGltZSgpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdCTE9RVUVBRE8gcG9yIHNlbWFuYSBlbiBlbCBwYXNhZG8nKTtcbiAgICAgICAgY29uc29sZS5sb2coJz09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09Jyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnTkFWRUdBQ0nDk04gUEVSTUlUSURBIC0gYWN0dWFsaXphbmRvIGN1cnJlbnRXZWVrJyk7XG4gICAgY29uc29sZS5sb2coJz09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09Jyk7XG4gICAgc2V0Q3VycmVudFdlZWsobmV3V2Vlayk7XG4gIH07XG5cbiAgLyoqXG4gICAqIEZvcm1hdGVhciByYW5nbyBkZSBzZW1hbmFcbiAgICovXG4gIGNvbnN0IGZvcm1hdFdlZWtSYW5nZSA9IChzdGFydERhdGU6IERhdGUpID0+IHtcbiAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUoc3RhcnREYXRlKTtcbiAgICBlbmREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSArIDYpO1xuICAgIFxuICAgIGNvbnN0IHN0YXJ0RGF5ID0gc3RhcnREYXRlLmdldERhdGUoKTtcbiAgICBjb25zdCBlbmREYXkgPSBlbmREYXRlLmdldERhdGUoKTtcbiAgICBjb25zdCBtb250aCA9IHN0YXJ0RGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ2VzLUVTJywgeyBtb250aDogJ2xvbmcnIH0pO1xuICAgIGNvbnN0IHllYXIgPSBzdGFydERhdGUuZ2V0RnVsbFllYXIoKTtcbiAgICBcbiAgICByZXR1cm4gYCR7c3RhcnREYXl9LSR7ZW5kRGF5fSAke21vbnRofSAke3llYXJ9YDtcbiAgfTtcblxuICAvKipcbiAgICogTWFuZWphciBzZWxlY2Npw7NuIGRlIGZlY2hhXG4gICAqL1xuICBjb25zdCBoYW5kbGVEYXRlU2VsZWN0ID0gKGRhdGU6IHN0cmluZykgPT4ge1xuICAgIC8vIERFQlVHOiBMb2cgc2VsZWNjacOzbiBkZSBmZWNoYSBjb24gYW7DoWxpc2lzIHRpbWV6b25lXG4gICAgY29uc29sZS5sb2coJz09PSBERUJVRyBTRUxFQ0NJw5NOIEZFQ0hBIChUSU1FWk9ORS1TQUZFKSA9PT0nKTtcbiAgICBjb25zb2xlLmxvZygnRmVjaGEgc2VsZWNjaW9uYWRhIChzdHJpbmcpOicsIGRhdGUpO1xuXG4gICAgLy8gQ1JJVElDQUwgRklYOiBDcmVhdGUgdGltZXpvbmUtc2FmZSBEYXRlIG9iamVjdFxuICAgIC8vIFByb2JsZW06IG5ldyBEYXRlKFwiMjAyNS0wNS0yOVwiKSBjcmVhdGVzIE1heSAyOCBpbiBHTVQtMDUwMFxuICAgIC8vIFNvbHV0aW9uOiBQYXJzZSBkYXRlIGNvbXBvbmVudHMgbWFudWFsbHkgdG8gYXZvaWQgVVRDIGludGVycHJldGF0aW9uXG4gICAgY29uc3QgW3llYXIsIG1vbnRoLCBkYXldID0gZGF0ZS5zcGxpdCgnLScpLm1hcChOdW1iZXIpO1xuICAgIGNvbnN0IGRhdGVPYmogPSBuZXcgRGF0ZSh5ZWFyLCBtb250aCAtIDEsIGRheSk7IC8vIG1vbnRoIGlzIDAtaW5kZXhlZFxuICAgIGNvbnN0IGxvY2FsRGF0ZVN0cmluZyA9IGAke2RhdGVPYmouZ2V0RnVsbFllYXIoKX0tJHtTdHJpbmcoZGF0ZU9iai5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKX0tJHtTdHJpbmcoZGF0ZU9iai5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyl9YDtcblxuICAgIC8vIEFsc28gY3JlYXRlIFVUQyB2ZXJzaW9uIGZvciBjb21wYXJpc29uXG4gICAgY29uc3QgZGF0ZU9ialVUQyA9IG5ldyBEYXRlKGRhdGUpOyAvLyBUaGlzIGNyZWF0ZXMgdGhlIHByb2JsZW1hdGljIFVUQyBpbnRlcnByZXRhdGlvblxuXG4gICAgY29uc29sZS5sb2coJ0RhdGUgb2JqZWN0IGNyZWFkbyAodGltZXpvbmUtc2FmZSk6JywgZGF0ZU9iaik7XG4gICAgY29uc29sZS5sb2coJ0RhdGUgb2JqZWN0IGNyZWFkbyAoVVRDIGludGVycHJldGF0aW9uKTonLCBkYXRlT2JqVVRDKTtcbiAgICBjb25zb2xlLmxvZygnRGF0ZSBvYmplY3QgSVNPIChVVEMpOicsIGRhdGVPYmpVVEMudG9JU09TdHJpbmcoKSk7XG4gICAgY29uc29sZS5sb2coJ0RhdGUgb2JqZWN0IGxvY2FsIHN0cmluZyAodGltZXpvbmUtc2FmZSk6JywgbG9jYWxEYXRlU3RyaW5nKTtcbiAgICBjb25zb2xlLmxvZygnVGltZXpvbmUgb2Zmc2V0IChtaW51dGVzKTonLCBkYXRlT2JqLmdldFRpbWV6b25lT2Zmc2V0KCkpO1xuXG4gICAgLy8gQ1JJVElDQUwgRklYOiBDb3JyZWN0IHRpbWV6b25lIGRlc2Zhc2UgZGV0ZWN0aW9uIGxvZ2ljIHVzaW5nIHRpbWV6b25lLXNhZmUgb2JqZWN0c1xuICAgIGNvbnN0IHV0Y0RhdGVTdHJpbmdGcm9tVVRDID0gZGF0ZU9ialVUQy50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG4gICAgY29uc3QgdXRjRGF0ZVN0cmluZ0Zyb21Mb2NhbCA9IGRhdGVPYmoudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgIGNvbnN0IGhhc1RpbWV6b25lRGVzZmFzZSA9IGRhdGUgIT09IHV0Y0RhdGVTdHJpbmdGcm9tVVRDO1xuXG4gICAgY29uc29sZS5sb2coJ0NvbXBhcmFjacOzbiB0aW1lem9uZSAoQ09SUkVHSURBKTonKTtcbiAgICBjb25zb2xlLmxvZygnICAtIGRhdGUgKGlucHV0KTonLCBkYXRlKTtcbiAgICBjb25zb2xlLmxvZygnICAtIHV0Y0RhdGVTdHJpbmcgKGZyb20gVVRDIG9iaik6JywgdXRjRGF0ZVN0cmluZ0Zyb21VVEMpO1xuICAgIGNvbnNvbGUubG9nKCcgIC0gdXRjRGF0ZVN0cmluZyAoZnJvbSBsb2NhbCBvYmopOicsIHV0Y0RhdGVTdHJpbmdGcm9tTG9jYWwpO1xuICAgIGNvbnNvbGUubG9nKCcgIC0gbG9jYWxEYXRlU3RyaW5nICh0aW1lem9uZS1zYWZlKTonLCBsb2NhbERhdGVTdHJpbmcpO1xuICAgIGNvbnNvbGUubG9nKCfCv0hheSBkZXNmYXNlIHRpbWV6b25lPzonLCBoYXNUaW1lem9uZURlc2Zhc2UpO1xuICAgIGNvbnNvbGUubG9nKCfCv0RhdGUgb2JqZWN0cyBzb24gY29uc2lzdGVudGVzPzonLCBsb2NhbERhdGVTdHJpbmcgPT09IGRhdGUpO1xuXG4gICAgY29uc29sZS5sb2coJ21pbkRhdGU6JywgbWluRGF0ZSk7XG4gICAgY29uc29sZS5sb2coJ0NvbXBhcmFjacOzbiBkYXRlIDwgbWluRGF0ZTonLCBkYXRlIDwgbWluRGF0ZSk7XG5cbiAgICAvLyBWYWxpZGFyIGZlY2hhIG3DrW5pbWFcbiAgICBpZiAobWluRGF0ZSAmJiBkYXRlIDwgbWluRGF0ZSkge1xuICAgICAgY29uc29sZS5sb2coJ0JMT1FVRUFETyBwb3IgbWluRGF0ZScpO1xuICAgICAgY29uc29sZS5sb2coJz09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdMTEFNQU5ETyBvbkRhdGVTZWxlY3QgY29uIGZlY2hhIHRpbWV6b25lLXNhZmU6JywgZGF0ZSk7XG4gICAgb25EYXRlU2VsZWN0KGRhdGUpO1xuICAgIGNvbnNvbGUubG9nKCc9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0nKTtcbiAgfTtcblxuICAvKipcbiAgICogR2VuZXJhciBzdWdlcmVuY2lhcyBpbnRlbGlnZW50ZXMgdXNhbmRvIFNtYXJ0U3VnZ2VzdGlvbnNFbmdpbmVcbiAgICovXG4gIGNvbnN0IGdlbmVyYXRlU21hcnRTdWdnZXN0aW9ucyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWVuYWJsZVNtYXJ0U3VnZ2VzdGlvbnMgfHwgIWFpQ29udGV4dCB8fCB3ZWVrRGF0YS5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRMb2FkaW5nU3VnZ2VzdGlvbnModHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3VnZ2VzdGlvbnNFbmdpbmUgPSBuZXcgU21hcnRTdWdnZXN0aW9uc0VuZ2luZShvcmdhbml6YXRpb25JZCk7XG5cbiAgICAgIC8vIENvbnZlcnRpciB3ZWVrRGF0YSBhIGZvcm1hdG8gZGUgb3BjaW9uZXMgZGlzcG9uaWJsZXNcbiAgICAgIGNvbnN0IGF2YWlsYWJsZU9wdGlvbnMgPSB3ZWVrRGF0YVxuICAgICAgICAuZmlsdGVyKGRheSA9PiBkYXkuYXZhaWxhYmlsaXR5TGV2ZWwgIT09ICdub25lJylcbiAgICAgICAgLmZsYXRNYXAoZGF5ID0+XG4gICAgICAgICAgZGF5LnNsb3RzPy5tYXAoc2xvdCA9PiAoe1xuICAgICAgICAgICAgZGF0ZTogZGF5LmRhdGUsXG4gICAgICAgICAgICB0aW1lOiBzbG90LnRpbWUsXG4gICAgICAgICAgICBkb2N0b3JJZDogc2xvdC5kb2N0b3JJZCxcbiAgICAgICAgICAgIGRvY3Rvck5hbWU6IHNsb3QuZG9jdG9yTmFtZSxcbiAgICAgICAgICAgIGF2YWlsYWJsZTogc2xvdC5hdmFpbGFibGUsXG4gICAgICAgICAgICBwcmljZTogc2xvdC5wcmljZVxuICAgICAgICAgIH0pKSB8fCBbe1xuICAgICAgICAgICAgZGF0ZTogZGF5LmRhdGUsXG4gICAgICAgICAgICB0aW1lOiAnMDk6MDAnLCAvLyBUaWVtcG8gcG9yIGRlZmVjdG9cbiAgICAgICAgICAgIGF2YWlsYWJsZTogdHJ1ZVxuICAgICAgICAgIH1dXG4gICAgICAgICk7XG5cbiAgICAgIGlmIChhdmFpbGFibGVPcHRpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBzZXRTbWFydFN1Z2dlc3Rpb25zKG51bGwpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHN1Z2dlc3Rpb25zRW5naW5lLmdlbmVyYXRlU3VnZ2VzdGlvbnMoXG4gICAgICAgIGFpQ29udGV4dCxcbiAgICAgICAgYXZhaWxhYmxlT3B0aW9uc1xuICAgICAgKTtcblxuICAgICAgc2V0U21hcnRTdWdnZXN0aW9ucyhyZXN1bHQpO1xuICAgICAgc2V0U2hvd1NtYXJ0U3VnZ2VzdGlvbnModHJ1ZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgc21hcnQgc3VnZ2VzdGlvbnM6JywgZXJyb3IpO1xuICAgICAgc2V0U21hcnRTdWdnZXN0aW9ucyhudWxsKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZ1N1Z2dlc3Rpb25zKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtlbmFibGVTbWFydFN1Z2dlc3Rpb25zLCBhaUNvbnRleHQsIHdlZWtEYXRhLCBvcmdhbml6YXRpb25JZF0pO1xuXG4gIC8qKlxuICAgKiBPYnRlbmVyIHN1Z2VyZW5jaWFzIGRlIElBIGLDoXNpY2FzIChmYWxsYmFjaylcbiAgICovXG4gIGNvbnN0IGdldEFJU3VnZ2VzdGlvbnMgPSAoKSA9PiB7XG4gICAgaWYgKCFlbmFibGVTbWFydFN1Z2dlc3Rpb25zIHx8ICFhaUNvbnRleHQ/LnN1Z2dlc3RlZERhdGVzKSB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuXG4gICAgcmV0dXJuIHdlZWtEYXRhLmZpbHRlcihkYXkgPT5cbiAgICAgIGFpQ29udGV4dC5zdWdnZXN0ZWREYXRlcz8uaW5jbHVkZXMoZGF5LmRhdGUpICYmXG4gICAgICBkYXkuYXZhaWxhYmlsaXR5TGV2ZWwgIT09ICdub25lJ1xuICAgICk7XG4gIH07XG5cbiAgY29uc3QgYWlTdWdnZXN0aW9ucyA9IGdldEFJU3VnZ2VzdGlvbnMoKTtcblxuICAvLyBHZW5lcmFyIHN1Z2VyZW5jaWFzIGludGVsaWdlbnRlcyBjdWFuZG8gY2FtYmlhbiBsb3MgZGF0b3NcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZW5hYmxlU21hcnRTdWdnZXN0aW9ucyAmJiBhaUNvbnRleHQgJiYgd2Vla0RhdGEubGVuZ3RoID4gMCkge1xuICAgICAgZ2VuZXJhdGVTbWFydFN1Z2dlc3Rpb25zKCk7XG4gICAgfVxuICB9LCBbZ2VuZXJhdGVTbWFydFN1Z2dlc3Rpb25zXSk7XG5cbiAgLyoqXG4gICAqIE1hbmVqYXIgc2VsZWNjacOzbiBkZSBzdWdlcmVuY2lhIGludGVsaWdlbnRlXG4gICAqL1xuICBjb25zdCBoYW5kbGVTbWFydFN1Z2dlc3Rpb25TZWxlY3QgPSAoc3VnZ2VzdGlvbjogU21hcnRTdWdnZXN0aW9uKSA9PiB7XG4gICAgaWYgKHN1Z2dlc3Rpb24uZGF0YS5kYXRlKSB7XG4gICAgICBoYW5kbGVEYXRlU2VsZWN0KHN1Z2dlc3Rpb24uZGF0YS5kYXRlKTtcbiAgICB9XG4gICAgc2V0U2hvd1NtYXJ0U3VnZ2VzdGlvbnMoZmFsc2UpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNiAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgIHt0aXRsZX1cbiAgICAgICAgPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPntzdWJ0aXRsZX08L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFNtYXJ0IEFJIFN1Z2dlc3Rpb25zIChGYXNlIDMpICovfVxuICAgICAge2VuYWJsZVNtYXJ0U3VnZ2VzdGlvbnMgJiYgc2hvd1NtYXJ0U3VnZ2VzdGlvbnMgJiYgc21hcnRTdWdnZXN0aW9ucyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIDxTbWFydFN1Z2dlc3Rpb25zRGlzcGxheVxuICAgICAgICAgICAgc3VnZ2VzdGlvbnNSZXN1bHQ9e3NtYXJ0U3VnZ2VzdGlvbnN9XG4gICAgICAgICAgICBvblN1Z2dlc3Rpb25TZWxlY3Q9e2hhbmRsZVNtYXJ0U3VnZ2VzdGlvblNlbGVjdH1cbiAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmdTdWdnZXN0aW9uc31cbiAgICAgICAgICAgIHNob3dNZXRyaWNzPXtmYWxzZX1cbiAgICAgICAgICAgIGNvbXBhY3Q9e2NvbXBhY3RTdWdnZXN0aW9uc31cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBBSSBTdWdnZXN0aW9ucyBGYWxsYmFjayAoRmFzZSAyKSAqL31cbiAgICAgIHtlbmFibGVTbWFydFN1Z2dlc3Rpb25zICYmICFzaG93U21hcnRTdWdnZXN0aW9ucyAmJiBhaVN1Z2dlc3Rpb25zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAgdG8tYmx1ZS01MCByb3VuZGVkLWxnIHAtNCBtYi02XCI+XG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTMgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxaYXAgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICAgICAgICBTdWdlcmVuY2lhcyBpbnRlbGlnZW50ZXNcbiAgICAgICAgICA8L2g0PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtM1wiPlxuICAgICAgICAgICAge2FpU3VnZ2VzdGlvbnMuc2xpY2UoMCwgMykubWFwKChzdWdnZXN0aW9uLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtzdWdnZXN0aW9uLmRhdGV9XG4gICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGF0ZVNlbGVjdChzdWdnZXN0aW9uLmRhdGUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMyBiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlci0yIGJvcmRlci1wdXJwbGUtMjAwIGhvdmVyOmJvcmRlci1wdXJwbGUtNDAwIHRyYW5zaXRpb24tY29sb3JzIHRleHQtbGVmdFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAge3N1Z2dlc3Rpb24uaXNUb2RheSA/ICdIb3knIDogc3VnZ2VzdGlvbi5pc1RvbW9ycm93ID8gJ01hw7FhbmEnIDogc3VnZ2VzdGlvbi5kYXlOYW1lfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwXCI+e3N1Z2dlc3Rpb24uZGF0ZX08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1wdXJwbGUtNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIHtpbmRleCA9PT0gMCAmJiAn4q2QIFJlY29tZW5kYWRvJ31cbiAgICAgICAgICAgICAgICAgIHtpbmRleCA9PT0gMSAmJiAn8J+VkCBGbGV4aWJsZSd9XG4gICAgICAgICAgICAgICAgICB7aW5kZXggPT09IDIgJiYgJ/CfmoAgUHLDs3hpbW8nfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBMb2FkaW5nIFNtYXJ0IFN1Z2dlc3Rpb25zICovfVxuICAgICAge2VuYWJsZVNtYXJ0U3VnZ2VzdGlvbnMgJiYgbG9hZGluZ1N1Z2dlc3Rpb25zICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNTAgYm9yZGVyIGJvcmRlci1wdXJwbGUtMjAwIHJvdW5kZWQtbGcgcC00IG1iLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLWItMiBib3JkZXItcHVycGxlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtODAwIGZvbnQtbWVkaXVtXCI+R2VuZXJhbmRvIHN1Z2VyZW5jaWFzIGludGVsaWdlbnRlcy4uLjwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogV2Vla2x5IE5hdmlnYXRpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZVdlZWsoJ3ByZXYnKX1cbiAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICA+XG4gICAgICAgICAgPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgQW50ZXJpb3JcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIFxuICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICB7Zm9ybWF0V2Vla1JhbmdlKGN1cnJlbnRXZWVrKX1cbiAgICAgICAgPC9oND5cbiAgICAgICAgXG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZVdlZWsoJ25leHQnKX1cbiAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICA+XG4gICAgICAgICAgU2lndWllbnRlXG4gICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTFcIiAvPlxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTG9hZGluZyBTdGF0ZSAqL31cbiAgICAgIHtpc0xvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBtci0zXCI+PC9kaXY+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNhcmdhbmRvIGRpc3BvbmliaWxpZGFkLi4uPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBFcnJvciBTdGF0ZSAqL31cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNCBtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNzAwIGZvbnQtbWVkaXVtXCI+RXJyb3IgY2FyZ2FuZG8gZGlzcG9uaWJpbGlkYWQ8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIHRleHQtc20gbXQtMVwiPntlcnJvcn08L3A+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICBvbkNsaWNrPXtyZWZldGNofVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtIHRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDAgdW5kZXJsaW5lXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBJbnRlbnRhciBkZSBudWV2b1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBXZWVrbHkgQXZhaWxhYmlsaXR5IEluZGljYXRvcnMgKi99XG4gICAgICB7IWlzTG9hZGluZyAmJiAhZXJyb3IgJiYgd2Vla0RhdGEubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIDxXZWVrbHlBdmFpbGFiaWxpdHlcbiAgICAgICAgICAgIHdlZWtEYXRhPXt3ZWVrRGF0YS5tYXAoZGF5ID0+ICh7XG4gICAgICAgICAgICAgIGRhdGU6IGRheS5kYXRlLFxuICAgICAgICAgICAgICBkYXlOYW1lOiBkYXkuZGF5TmFtZSxcbiAgICAgICAgICAgICAgc2xvdHNDb3VudDogZGF5LnNsb3RzQ291bnRcbiAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgIHNlbGVjdGVkRGF0ZT17c2VsZWN0ZWREYXRlfVxuICAgICAgICAgICAgb25EYXRlU2VsZWN0PXtoYW5kbGVEYXRlU2VsZWN0fVxuICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBEZW5zaXR5IExlZ2VuZCAoc2kgZXN0w6EgaGFiaWxpdGFkbykgKi99XG4gICAgICB7c2hvd0RlbnNpdHlJbmRpY2F0b3JzICYmICFpc0xvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTNcIj5EaXNwb25pYmlsaWRhZDo8L2g1PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtMyB0ZXh0LXhzXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkFsdGEgKDYrIHNsb3RzKTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYmcteWVsbG93LTUwMCByb3VuZGVkLWZ1bGwgbXItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TWVkaWEgKDMtNSBzbG90cyk8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkJhamEgKDEtMiBzbG90cyk8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLWdyYXktNDAwIHJvdW5kZWQtZnVsbCBtci0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5ObyBkaXNwb25pYmxlPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIEVtcHR5IFN0YXRlICovfVxuICAgICAgeyFpc0xvYWRpbmcgJiYgIWVycm9yICYmIHdlZWtEYXRhLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlNpbiBkaXNwb25pYmlsaWRhZDwvaDQ+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPk5vIGhheSBob3JhcmlvcyBkaXNwb25pYmxlcyBwYXJhIGVzdGEgc2VtYW5hLjwvcD5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG5hdmlnYXRlV2VlaygnbmV4dCcpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS03MDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIFZlciBwcsOzeGltYSBzZW1hbmFcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgV2Vla2x5QXZhaWxhYmlsaXR5U2VsZWN0b3I7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlTWVtbyIsIkNoZXZyb25MZWZ0IiwiQ2hldnJvblJpZ2h0IiwiQ2FsZW5kYXIiLCJDbG9jayIsIlphcCIsIkFsZXJ0Q2lyY2xlIiwiV2Vla2x5QXZhaWxhYmlsaXR5IiwiU21hcnRTdWdnZXN0aW9uc0Rpc3BsYXkiLCJTbWFydFN1Z2dlc3Rpb25zRW5naW5lIiwidmFsaWRhdGVNdWx0aXBsZURhdGVzIiwidXNlV2Vla2x5QXZhaWxhYmlsaXR5RGF0YSIsInN0YXJ0RGF0ZSIsIm9yZ2FuaXphdGlvbklkIiwic2VydmljZUlkIiwiZG9jdG9ySWQiLCJsb2NhdGlvbklkIiwib25Mb2FkQXZhaWxhYmlsaXR5Iiwid2Vla0RhdGEiLCJzZXRXZWVrRGF0YSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImxvYWRXZWVrRGF0YSIsIm1vY2tEYXRhIiwiZGF5TmFtZXMiLCJjb25zb2xlIiwibG9nIiwidG9JU09TdHJpbmciLCJnZXRUaW1lem9uZU9mZnNldCIsImkiLCJkYXRlIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwiZ2V0TW9udGgiLCJnZXREYXRlIiwic3RhcnREYXRlWWVhciIsInN0YXJ0RGF0ZU1vbnRoIiwic3RhcnREYXRlRGF0ZSIsImluZGV4SSIsImNhbGN1bGF0aW9uIiwibmV3RGF0ZSIsImdldERheSIsImRheU5hbWUiLCJsb2NhbERhdGVTdHJpbmciLCJTdHJpbmciLCJwYWRTdGFydCIsInRvZGF5Iiwic2V0SG91cnMiLCJpc1RvZGF5IiwiZ2V0VGltZSIsImlzUGFzdERhdGUiLCJ0b21vcnJvdyIsInNldERhdGUiLCJpc1RvbW9ycm93IiwiaXNXZWVrZW5kIiwic2xvdHNDb3VudCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImF2YWlsYWJpbGl0eUxldmVsIiwiZmluYWxEYXRlU3RyaW5nIiwiZGF0ZUlTTyIsInNwbGl0IiwiZGF0ZUxvY2FsIiwidGltZXpvbmVDb21wYXJpc29uIiwiaXNvIiwibG9jYWwiLCJtYXRjaCIsInB1c2giLCJlbmREYXRlIiwiZGF0YSIsImVyciIsIkVycm9yIiwibWVzc2FnZSIsInJlZmV0Y2giLCJXZWVrbHlBdmFpbGFiaWxpdHlTZWxlY3RvciIsInRpdGxlIiwic3VidGl0bGUiLCJzZWxlY3RlZERhdGUiLCJvbkRhdGVTZWxlY3QiLCJtaW5EYXRlIiwic2hvd0RlbnNpdHlJbmRpY2F0b3JzIiwiZW5hYmxlU21hcnRTdWdnZXN0aW9ucyIsImFpQ29udGV4dCIsImVudHJ5TW9kZSIsImNvbXBhY3RTdWdnZXN0aW9ucyIsImV4dGVybmFsTG9hZGluZyIsImNsYXNzTmFtZSIsImN1cnJlbnRXZWVrIiwic2V0Q3VycmVudFdlZWsiLCJzdGFydE9mV2VlayIsImRhdGFMb2FkaW5nIiwic21hcnRTdWdnZXN0aW9ucyIsInNldFNtYXJ0U3VnZ2VzdGlvbnMiLCJsb2FkaW5nU3VnZ2VzdGlvbnMiLCJzZXRMb2FkaW5nU3VnZ2VzdGlvbnMiLCJzaG93U21hcnRTdWdnZXN0aW9ucyIsInNldFNob3dTbWFydFN1Z2dlc3Rpb25zIiwiaXNMb2FkaW5nIiwiZGF0ZVZhbGlkYXRpb25SZXN1bHRzIiwibGVuZ3RoIiwiZGF0ZXMiLCJtYXAiLCJkYXkiLCJhdmFpbGFibGVTbG90c0J5RGF0ZSIsImZvckVhY2giLCJ0aW1lIiwiYXZhaWxhYmxlIiwidmFsaWRhdGlvblJlc3VsdHMiLCJlbmhhbmNlZFdlZWtEYXRhIiwidmFsaWRhdGlvbiIsImlzQmxvY2tlZCIsImlzVmFsaWQiLCJibG9ja1JlYXNvbiIsInJlYXNvbiIsInZhbGlkYXRpb25SZXN1bHQiLCJuYXZpZ2F0ZVdlZWsiLCJkaXJlY3Rpb24iLCJuZXdXZWVrIiwibWluRGF0ZU9iaiIsImVuZE9mTmV3V2VlayIsImZvcm1hdFdlZWtSYW5nZSIsInN0YXJ0RGF5IiwiZW5kRGF5IiwibW9udGgiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ5ZWFyIiwiaGFuZGxlRGF0ZVNlbGVjdCIsIk51bWJlciIsImRhdGVPYmoiLCJkYXRlT2JqVVRDIiwidXRjRGF0ZVN0cmluZ0Zyb21VVEMiLCJ1dGNEYXRlU3RyaW5nRnJvbUxvY2FsIiwiaGFzVGltZXpvbmVEZXNmYXNlIiwiZ2VuZXJhdGVTbWFydFN1Z2dlc3Rpb25zIiwic3VnZ2VzdGlvbnNFbmdpbmUiLCJhdmFpbGFibGVPcHRpb25zIiwiZmlsdGVyIiwiZmxhdE1hcCIsInNsb3RzIiwic2xvdCIsImRvY3Rvck5hbWUiLCJwcmljZSIsInJlc3VsdCIsImdlbmVyYXRlU3VnZ2VzdGlvbnMiLCJnZXRBSVN1Z2dlc3Rpb25zIiwic3VnZ2VzdGVkRGF0ZXMiLCJpbmNsdWRlcyIsImFpU3VnZ2VzdGlvbnMiLCJoYW5kbGVTbWFydFN1Z2dlc3Rpb25TZWxlY3QiLCJzdWdnZXN0aW9uIiwiZGl2IiwiaDMiLCJwIiwic3VnZ2VzdGlvbnNSZXN1bHQiLCJvblN1Z2dlc3Rpb25TZWxlY3QiLCJzaG93TWV0cmljcyIsImNvbXBhY3QiLCJoNCIsInNsaWNlIiwiaW5kZXgiLCJidXR0b24iLCJ0eXBlIiwib25DbGljayIsInNwYW4iLCJkaXNhYmxlZCIsInNpemUiLCJoNSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils/dateValidation.ts":
/*!*****************************************!*\
  !*** ./src/lib/utils/dateValidation.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canBookSameDayAppointments: function() { return /* binding */ canBookSameDayAppointments; },\n/* harmony export */   getBlockedDateAriaLabel: function() { return /* binding */ getBlockedDateAriaLabel; },\n/* harmony export */   getBlockedDateMessage: function() { return /* binding */ getBlockedDateMessage; },\n/* harmony export */   validateDateAvailability: function() { return /* binding */ validateDateAvailability; },\n/* harmony export */   validateMultipleDates: function() { return /* binding */ validateMultipleDates; }\n/* harmony export */ });\n/**\n * Date Validation Utilities for UI-level date blocking\n * \n * Implements the same 4-hour advance booking rule as SmartSuggestionsEngine\n * but optimized for UI validation and user experience\n */ /**\n * Validates if a date has any valid appointment slots based on 4-hour advance booking rule\n * \n * @param date - Date string in YYYY-MM-DD format\n * @param availableSlots - Array of available time slots for the date\n * @returns Validation result with details\n */ function validateDateAvailability(date) {\n    let availableSlots = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    const MINIMUM_ADVANCE_HOURS = 4;\n    const MINIMUM_ADVANCE_MINUTES = MINIMUM_ADVANCE_HOURS * 60;\n    try {\n        const now = new Date();\n        // Parse date components using timezone-safe method (consistent with our timezone fixes)\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day);\n        // Check if date is in the past\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        dateObj.setHours(0, 0, 0, 0);\n        if (dateObj.getTime() < today.getTime()) {\n            return {\n                isValid: false,\n                reason: \"Fecha pasada - No se pueden agendar citas en fechas anteriores\"\n            };\n        }\n        // If no slots provided, generate typical business hours for validation\n        const slotsToCheck = availableSlots.length > 0 ? availableSlots : generateTypicalBusinessHours(date);\n        // Check each time slot against 4-hour rule\n        const validSlots = slotsToCheck.filter((slot)=>{\n            if (!slot.available) return false;\n            const [hours, minutes] = slot.time.split(\":\").map(Number);\n            const slotDateTime = new Date(year, month - 1, day, hours, minutes);\n            const timeDifferenceMs = slotDateTime.getTime() - now.getTime();\n            const timeDifferenceMinutes = Math.floor(timeDifferenceMs / (1000 * 60));\n            return timeDifferenceMinutes >= MINIMUM_ADVANCE_MINUTES;\n        });\n        if (validSlots.length === 0) {\n            // Calculate when the date becomes valid\n            const nextValidTime = calculateNextValidTime(date);\n            const hoursUntilValid = calculateHoursUntilValid(date);\n            return {\n                isValid: false,\n                reason: hoursUntilValid > 0 ? \"Reserva con m\\xednimo \".concat(MINIMUM_ADVANCE_HOURS, \" horas de anticipaci\\xf3n requerida\") : \"No hay horarios disponibles que cumplan con la anticipaci\\xf3n m\\xednima\",\n                hoursUntilValid,\n                nextValidTime\n            };\n        }\n        return {\n            isValid: true\n        };\n    } catch (error) {\n        console.error(\"Error validating date availability:\", error);\n        return {\n            isValid: false,\n            reason: \"Error validando disponibilidad de fecha\"\n        };\n    }\n}\n/**\n * Generates typical business hours for a date (used when no specific slots provided)\n */ function generateTypicalBusinessHours(date) {\n    const businessHours = [\n        \"08:00\",\n        \"09:00\",\n        \"10:00\",\n        \"11:00\",\n        \"14:00\",\n        \"15:00\",\n        \"16:00\",\n        \"17:00\",\n        \"18:00\"\n    ];\n    return businessHours.map((time)=>({\n            date,\n            time,\n            available: true\n        }));\n}\n/**\n * Calculates the next valid time for a date\n */ function calculateNextValidTime(date) {\n    const MINIMUM_ADVANCE_HOURS = 4;\n    const now = new Date();\n    const [year, month, day] = date.split(\"-\").map(Number);\n    // For today, calculate 4 hours from now\n    const currentDate = now.toISOString().split(\"T\")[0];\n    if (date === currentDate) {\n        const nextValidDateTime = new Date(now.getTime() + MINIMUM_ADVANCE_HOURS * 60 * 60 * 1000);\n        return nextValidDateTime.toLocaleTimeString(\"es-ES\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: false\n        });\n    }\n    // For future dates, first available business hour\n    return \"08:00\";\n}\n/**\n * Calculates hours until a date becomes valid\n */ function calculateHoursUntilValid(date) {\n    const now = new Date();\n    const [year, month, day] = date.split(\"-\").map(Number);\n    const dateObj = new Date(year, month - 1, day, 8, 0); // 8 AM start\n    const timeDifferenceMs = dateObj.getTime() - now.getTime();\n    const hoursUntilValid = Math.ceil(timeDifferenceMs / (1000 * 60 * 60));\n    return Math.max(0, hoursUntilValid);\n}\n/**\n * Validates multiple dates and returns a map of validation results\n */ function validateMultipleDates(dates) {\n    let availableSlotsByDate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const results = {};\n    dates.forEach((date)=>{\n        const slots = availableSlotsByDate[date] || [];\n        results[date] = validateDateAvailability(date, slots);\n    });\n    return results;\n}\n/**\n * Checks if current time allows for same-day appointments\n */ function canBookSameDayAppointments() {\n    const now = new Date();\n    const currentHour = now.getHours();\n    // If it's before 4 PM (16:00), there might be valid slots for today\n    // (considering 4-hour rule and business hours until 8 PM)\n    return currentHour < 16;\n}\n/**\n * Gets user-friendly message for blocked dates\n */ function getBlockedDateMessage(validationResult) {\n    var _validationResult_reason;\n    if (validationResult.isValid) return \"\";\n    if ((_validationResult_reason = validationResult.reason) === null || _validationResult_reason === void 0 ? void 0 : _validationResult_reason.includes(\"Fecha pasada\")) {\n        return \"No disponible - Fecha pasada\";\n    }\n    if (validationResult.hoursUntilValid && validationResult.hoursUntilValid > 0) {\n        return \"No disponible - Reserva con 4 horas de anticipaci\\xf3n\";\n    }\n    return \"No disponible - Sin horarios v\\xe1lidos\";\n}\n/**\n * Gets accessibility label for blocked dates\n */ function getBlockedDateAriaLabel(date, validationResult) {\n    const dateObj = new Date(date);\n    const formattedDate = dateObj.toLocaleDateString(\"es-ES\", {\n        weekday: \"long\",\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n    const message = getBlockedDateMessage(validationResult);\n    return \"\".concat(formattedDate, \", \").concat(message);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/dateValidation.ts\n"));

/***/ })

});