"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/locations/route";
exports.ids = ["app/api/locations/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flocations%2Froute&page=%2Fapi%2Flocations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flocations%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flocations%2Froute&page=%2Fapi%2Flocations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flocations%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_locations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/locations/route.ts */ \"(rsc)/./src/app/api/locations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/locations/route\",\n        pathname: \"/api/locations\",\n        filename: \"route\",\n        bundlePath: \"app/api/locations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\api\\\\locations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_locations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/locations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flocations%2Froute&page=%2Fapi%2Flocations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flocations%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/locations/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/locations/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n/**\n * GET /api/locations\n * Fetch locations for an organization with filtering support\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const organizationId = searchParams.get(\"organizationId\");\n        const city = searchParams.get(\"city\");\n        const status = searchParams.get(\"status\");\n        const search = searchParams.get(\"search\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"100\");\n        if (!organizationId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Organization ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Get current user and verify permissions\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Build query\n        let query = supabase.from(\"locations\").select(\"*\").eq(\"organization_id\", organizationId).order(\"name\");\n        // Apply filters\n        if (city) {\n            query = query.eq(\"city\", city);\n        }\n        if (status === \"active\") {\n            query = query.eq(\"is_active\", true);\n        } else if (status === \"inactive\") {\n            query = query.eq(\"is_active\", false);\n        } else if (!status) {\n        // If no status filter, show all (including inactive for admin management)\n        // This is different from the original behavior which only showed active\n        }\n        if (search) {\n            query = query.or(`name.ilike.%${search}%,address.ilike.%${search}%,city.ilike.%${search}%,description.ilike.%${search}%`);\n        }\n        query = query.limit(limit);\n        const { data: locations, error } = await query;\n        if (error) {\n            console.error(\"Error fetching locations:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch locations\"\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            locations: locations || []\n        });\n    } catch (error) {\n        console.error(\"Unexpected error in locations API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/locations\n * Create a new location\n */ async function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, address, city, postal_code, phone, email, description, organization_id, is_active = true } = body;\n        // Validation\n        if (!name || !address || !organization_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Name, address, and organization_id are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Email validation if provided\n        if (email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid email format\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Get current user and verify permissions\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify user has admin access to the organization\n        const { data: profile } = await supabase.from(\"profiles\").select(\"role, organization_id\").eq(\"id\", user.id).single();\n        if (!profile || ![\n            \"admin\",\n            \"superadmin\"\n        ].includes(profile.role)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        // For admin users, ensure they can only create locations in their organization\n        if (profile.role === \"admin\" && profile.organization_id !== organization_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Cannot create locations for other organizations\"\n            }, {\n                status: 403\n            });\n        }\n        // Create the location\n        const { data: location, error } = await supabase.from(\"locations\").insert({\n            name,\n            address,\n            city,\n            postal_code,\n            phone,\n            email,\n            description,\n            organization_id,\n            is_active\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating location:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to create location\"\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            location\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/locations:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/locations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fjvletqwwmxusgthwphr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqdmxldHF3d214dXNndGh3cGhyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDc2MDAsImV4cCI6MjA2Mzc4MzYwMH0.TiU8DGo9kihikfmlk1drLs57tNuOrm_Pgq80yzsWytc\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set(name, value, options);\n                } catch  {\n                // The `set` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            },\n            remove (name, options) {\n                try {\n                    cookieStore.set(name, \"\", {\n                        ...options,\n                        maxAge: 0\n                    });\n                } catch  {\n                // The `remove` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFDWjtBQUdoQyxlQUFlRTtJQUNwQixNQUFNQyxjQUFjLE1BQU1GLHFEQUFPQTtJQUVqQyxPQUFPRCxpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLGtOQUF5QyxFQUN6QztRQUNFSCxTQUFTO1lBQ1BPLEtBQUlDLElBQVk7Z0JBQ2QsT0FBT04sWUFBWUssR0FBRyxDQUFDQyxPQUFPQztZQUNoQztZQUNBQyxLQUFJRixJQUFZLEVBQUVDLEtBQWEsRUFBRUUsT0FBWTtnQkFDM0MsSUFBSTtvQkFDRlQsWUFBWVEsR0FBRyxDQUFDRixNQUFNQyxPQUFPRTtnQkFDL0IsRUFBRSxPQUFNO2dCQUNOLHVEQUF1RDtnQkFDdkQsd0RBQXdEO2dCQUN4RCxpQkFBaUI7Z0JBQ25CO1lBQ0Y7WUFDQUMsUUFBT0osSUFBWSxFQUFFRyxPQUFZO2dCQUMvQixJQUFJO29CQUNGVCxZQUFZUSxHQUFHLENBQUNGLE1BQU0sSUFBSTt3QkFBRSxHQUFHRyxPQUFPO3dCQUFFRSxRQUFRO29CQUFFO2dCQUNwRCxFQUFFLE9BQU07Z0JBQ04sMERBQTBEO2dCQUMxRCx3REFBd0Q7Z0JBQ3hELGlCQUFpQjtnQkFDbkI7WUFDRjtRQUNGO0lBQ0Y7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnZW5kYWxvLy4vc3JjL2xpYi9zdXBhYmFzZS9zZXJ2ZXIudHM/MmU4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVTZXJ2ZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tICduZXh0L2hlYWRlcnMnO1xuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gJ0AvdHlwZXMvZGF0YWJhc2UnO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICBjb25zdCBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKTtcblxuICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50PERhdGFiYXNlPihcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICB7XG4gICAgICBjb29raWVzOiB7XG4gICAgICAgIGdldChuYW1lOiBzdHJpbmcpIHtcbiAgICAgICAgICByZXR1cm4gY29va2llU3RvcmUuZ2V0KG5hbWUpPy52YWx1ZTtcbiAgICAgICAgfSxcbiAgICAgICAgc2V0KG5hbWU6IHN0cmluZywgdmFsdWU6IHN0cmluZywgb3B0aW9uczogYW55KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucyk7XG4gICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAvLyBUaGUgYHNldGAgbWV0aG9kIHdhcyBjYWxsZWQgZnJvbSBhIFNlcnZlciBDb21wb25lbnQuXG4gICAgICAgICAgICAvLyBUaGlzIGNhbiBiZSBpZ25vcmVkIGlmIHlvdSBoYXZlIG1pZGRsZXdhcmUgcmVmcmVzaGluZ1xuICAgICAgICAgICAgLy8gdXNlciBzZXNzaW9ucy5cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHJlbW92ZShuYW1lOiBzdHJpbmcsIG9wdGlvbnM6IGFueSkge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb29raWVTdG9yZS5zZXQobmFtZSwgJycsIHsgLi4ub3B0aW9ucywgbWF4QWdlOiAwIH0pO1xuICAgICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgICAgLy8gVGhlIGByZW1vdmVgIG1ldGhvZCB3YXMgY2FsbGVkIGZyb20gYSBTZXJ2ZXIgQ29tcG9uZW50LlxuICAgICAgICAgICAgLy8gVGhpcyBjYW4gYmUgaWdub3JlZCBpZiB5b3UgaGF2ZSBtaWRkbGV3YXJlIHJlZnJlc2hpbmdcbiAgICAgICAgICAgIC8vIHVzZXIgc2Vzc2lvbnMuXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9XG4gICk7XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlU2VydmVyQ2xpZW50IiwiY29va2llcyIsImNyZWF0ZUNsaWVudCIsImNvb2tpZVN0b3JlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiZ2V0IiwibmFtZSIsInZhbHVlIiwic2V0Iiwib3B0aW9ucyIsInJlbW92ZSIsIm1heEFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/@opentelemetry","vendor-chunks/ramda","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flocations%2Froute&page=%2Fapi%2Flocations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flocations%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();