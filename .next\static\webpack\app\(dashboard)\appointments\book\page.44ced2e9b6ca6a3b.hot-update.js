"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA ===\");\n        console.log(\"Fecha seleccionada:\", date);\n        console.log(\"Fecha como Date object:\", new Date(date));\n        console.log(\"Fecha Date object ISO:\", new Date(date).toISOString());\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            return;\n        }\n        console.log(\"LLAMANDO onDateSelect con:\", date);\n        onDateSelect(date);\n        console.log(\"================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 497,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 510,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 540,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 577,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 585,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 604,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 603,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 621,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 619,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 644,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"4O/41eNoaHqwK0PAWV/sHVC2lvE=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});