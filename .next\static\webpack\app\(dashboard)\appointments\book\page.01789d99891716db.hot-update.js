"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                const date = new Date(startDate);\n                // DEBUG: Log antes de setDate\n                console.log(\"D\\xeda \".concat(i, \" (antes setDate):\"), {\n                    originalDate: date.toISOString(),\n                    startDateGetDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                date.setDate(startDate.getDate() + i);\n                // DEBUG: Log después de setDate\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s setDate):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    isoString: date.toISOString().split(\"T\")[0]\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                const finalDateString = date.toISOString().split(\"T\")[0];\n                // DEBUG: Log datos finales\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA ===\");\n        console.log(\"Fecha seleccionada:\", date);\n        console.log(\"Fecha como Date object:\", new Date(date));\n        console.log(\"Fecha Date object ISO:\", new Date(date).toISOString());\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            return;\n        }\n        console.log(\"LLAMANDO onDateSelect con:\", date);\n        onDateSelect(date);\n        console.log(\"================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 478,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 488,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 501,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 531,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 576,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 595,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 594,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 610,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 635,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 476,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"4O/41eNoaHqwK0PAWV/sHVC2lvE=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});