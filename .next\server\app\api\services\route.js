"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/services/route";
exports.ids = ["app/api/services/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fservices%2Froute&page=%2Fapi%2Fservices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservices%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fservices%2Froute&page=%2Fapi%2Fservices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservices%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_services_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/services/route.ts */ \"(rsc)/./src/app/api/services/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/services/route\",\n        pathname: \"/api/services\",\n        filename: \"route\",\n        bundlePath: \"app/api/services/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\api\\\\services\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_services_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/services/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fservices%2Froute&page=%2Fapi%2Fservices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservices%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/services/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/services/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n/**\n * GET /api/services\n * Fetch services for an organization with filtering support\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const organizationId = searchParams.get(\"organizationId\");\n        const category = searchParams.get(\"category\");\n        const status = searchParams.get(\"status\");\n        const search = searchParams.get(\"search\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"100\");\n        if (!organizationId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Organization ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Get current user and verify permissions\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Build query\n        let query = supabase.from(\"services\").select(\"*\").eq(\"organization_id\", organizationId).order(\"name\");\n        // Apply filters\n        if (category) {\n            query = query.eq(\"category\", category);\n        }\n        if (status === \"active\") {\n            query = query.eq(\"is_active\", true);\n        } else if (status === \"inactive\") {\n            query = query.eq(\"is_active\", false);\n        }\n        if (search) {\n            query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,category.ilike.%${search}%`);\n        }\n        query = query.limit(limit);\n        const { data: services, error } = await query;\n        if (error) {\n            console.error(\"Error fetching services:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch services\"\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            services: services || []\n        });\n    } catch (error) {\n        console.error(\"Unexpected error in services API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/services\n * Create a new service\n */ async function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, description, duration_minutes, price, category, organization_id, is_active = true } = body;\n        // Validation\n        if (!name || !duration_minutes || !organization_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Name, duration_minutes, and organization_id are required\"\n            }, {\n                status: 400\n            });\n        }\n        if (duration_minutes < 5 || duration_minutes > 480) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Duration must be between 5 and 480 minutes\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Get current user and verify permissions\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify user has admin access to the organization\n        const { data: profile } = await supabase.from(\"profiles\").select(\"role, organization_id\").eq(\"id\", user.id).single();\n        if (!profile || ![\n            \"admin\",\n            \"superadmin\"\n        ].includes(profile.role)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        // For admin users, ensure they can only create services in their organization\n        if (profile.role === \"admin\" && profile.organization_id !== organization_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Cannot create services for other organizations\"\n            }, {\n                status: 403\n            });\n        }\n        // Create the service\n        const { data: service, error } = await supabase.from(\"services\").insert({\n            name,\n            description,\n            duration_minutes,\n            price,\n            category,\n            organization_id,\n            is_active\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating service:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to create service\"\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            service\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/services:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/services/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fjvletqwwmxusgthwphr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqdmxldHF3d214dXNndGh3cGhyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDc2MDAsImV4cCI6MjA2Mzc4MzYwMH0.TiU8DGo9kihikfmlk1drLs57tNuOrm_Pgq80yzsWytc\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set(name, value, options);\n                } catch  {\n                // The `set` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            },\n            remove (name, options) {\n                try {\n                    cookieStore.set(name, \"\", {\n                        ...options,\n                        maxAge: 0\n                    });\n                } catch  {\n                // The `remove` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/@opentelemetry","vendor-chunks/ramda","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fservices%2Froute&page=%2Fapi%2Fservices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservices%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();