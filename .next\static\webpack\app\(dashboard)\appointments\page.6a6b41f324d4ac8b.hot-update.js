"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            for(let i = 0; i < 7; i++){\n                const date = new Date(startDate);\n                date.setDate(startDate.getDate() + i);\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                mockData.push({\n                    date: date.toISOString().split(\"T\")[0],\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            if (newWeek < minDateObj) {\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            return;\n        }\n        onDateSelect(date);\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 398,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 411,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 478,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 486,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 504,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 520,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 545,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 386,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"4O/41eNoaHqwK0PAWV/sHVC2lvE=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});