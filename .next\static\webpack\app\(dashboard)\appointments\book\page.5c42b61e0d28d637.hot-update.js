"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/UnifiedAppointmentFlow.tsx":
/*!****************************************************************!*\
  !*** ./src/components/appointments/UnifiedAppointmentFlow.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UnifiedAppointmentFlow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared */ \"(app-pages-browser)/./src/components/appointments/shared/index.ts\");\n/* harmony import */ var _FlowSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlowSelector */ \"(app-pages-browser)/./src/components/appointments/FlowSelector.tsx\");\n/* harmony import */ var _ExpressConfirmation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ExpressConfirmation */ \"(app-pages-browser)/./src/components/appointments/ExpressConfirmation.tsx\");\n/* harmony import */ var _ExpressSearchingState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ExpressSearchingState */ \"(app-pages-browser)/./src/components/appointments/ExpressSearchingState.tsx\");\n/* harmony import */ var _EnhancedTimeSlotSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./EnhancedTimeSlotSelector */ \"(app-pages-browser)/./src/components/appointments/EnhancedTimeSlotSelector.tsx\");\n/* harmony import */ var _WeeklyAvailabilitySelector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./WeeklyAvailabilitySelector */ \"(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\");\n/* harmony import */ var _lib_appointments_OptimalAppointmentFinder__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/appointments/OptimalAppointmentFinder */ \"(app-pages-browser)/./src/lib/appointments/OptimalAppointmentFinder.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * UnifiedAppointmentFlow Component\n * Harmonized appointment booking flow that works for both manual and AI modes\n * Follows PRD2.md specification: Service → Doctor (optional) → Location (optional) → Date → Time → Confirm\n */ \n\n\n\n\n\n\n\n\nfunction UnifiedAppointmentFlow(param) {\n    let { organizationId, userId, patientName, onAppointmentBooked, onCancel, initialData, mode = \"manual\" } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCancelDialog, setShowCancelDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Hybrid flow states\n    const [bookingFlow, setBookingFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optimalAppointment, setOptimalAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSearchingOptimal, setIsSearchingOptimal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Context for enhanced UX\n    const [aiContext, setAiContext] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((initialData === null || initialData === void 0 ? void 0 : initialData.aiContext) || null);\n    // Data states\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [doctors, setDoctors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availability, setAvailability] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Form data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        service_id: (initialData === null || initialData === void 0 ? void 0 : initialData.service_id) || \"\",\n        doctor_id: (initialData === null || initialData === void 0 ? void 0 : initialData.doctor_id) || \"\",\n        location_id: (initialData === null || initialData === void 0 ? void 0 : initialData.location_id) || \"\",\n        appointment_date: (initialData === null || initialData === void 0 ? void 0 : initialData.appointment_date) || \"\",\n        appointment_time: (initialData === null || initialData === void 0 ? void 0 : initialData.appointment_time) || \"\",\n        reason: (initialData === null || initialData === void 0 ? void 0 : initialData.reason) || \"\",\n        notes: (initialData === null || initialData === void 0 ? void 0 : initialData.notes) || \"\"\n    });\n    const [selectedSlot, setSelectedSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Define steps - hybrid flow includes flow selection\n    const getSteps = ()=>{\n        if (bookingFlow === \"express\") {\n            return [\n                {\n                    id: \"service\",\n                    title: \"Seleccionar Servicio\",\n                    completed: false,\n                    current: true\n                },\n                {\n                    id: \"flow\",\n                    title: \"Tipo de Reserva\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"confirm\",\n                    title: \"Confirmar Cita\",\n                    completed: false,\n                    current: false\n                }\n            ];\n        } else if (bookingFlow === \"personalized\") {\n            return [\n                {\n                    id: \"service\",\n                    title: \"Seleccionar Servicio\",\n                    completed: false,\n                    current: true\n                },\n                {\n                    id: \"flow\",\n                    title: \"Tipo de Reserva\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"doctor\",\n                    title: \"Elegir Doctor\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"location\",\n                    title: \"Seleccionar Sede\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"date\",\n                    title: \"Elegir Fecha\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"time\",\n                    title: \"Seleccionar Horario\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"confirm\",\n                    title: \"Confirmar Cita\",\n                    completed: false,\n                    current: false\n                }\n            ];\n        } else {\n            // Default flow before selection\n            return [\n                {\n                    id: \"service\",\n                    title: \"Seleccionar Servicio\",\n                    completed: false,\n                    current: true\n                },\n                {\n                    id: \"flow\",\n                    title: \"Tipo de Reserva\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"doctor\",\n                    title: \"Elegir Doctor\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"location\",\n                    title: \"Seleccionar Sede\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"date\",\n                    title: \"Elegir Fecha\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"time\",\n                    title: \"Seleccionar Horario\",\n                    completed: false,\n                    current: false\n                },\n                {\n                    id: \"confirm\",\n                    title: \"Confirmar Cita\",\n                    completed: false,\n                    current: false\n                }\n            ];\n        }\n    };\n    // Update step completion status\n    const updateSteps = ()=>{\n        const steps = getSteps();\n        return steps.map((step, index)=>({\n                ...step,\n                completed: index < currentStep,\n                current: index === currentStep\n            }));\n    };\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadServices();\n        loadLocations();\n    }, [\n        organizationId\n    ]);\n    const loadServices = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/services?organizationId=\".concat(organizationId));\n            if (response.ok) {\n                const data = await response.json();\n                setServices(data.services || []);\n            }\n        } catch (err) {\n            console.error(\"Error loading services:\", err);\n            setError(\"Error al cargar servicios\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadLocations = async ()=>{\n        try {\n            const response = await fetch(\"/api/locations?organizationId=\".concat(organizationId));\n            if (response.ok) {\n                const data = await response.json();\n                setLocations(data.locations || []);\n            }\n        } catch (err) {\n            console.error(\"Error loading locations:\", err);\n        }\n    };\n    const loadDoctors = async (serviceId)=>{\n        try {\n            setLoading(true);\n            let url = \"/api/doctors?organizationId=\".concat(organizationId);\n            if (serviceId) {\n                url += \"&serviceId=\".concat(serviceId);\n            }\n            const response = await fetch(url);\n            if (response.ok) {\n                var _data_data;\n                const data = await response.json();\n                // CRITICAL FIX: API returns data.data, not data.doctors\n                setDoctors(data.data || []);\n                console.log(\"DEBUG: Loaded \".concat(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0, \" doctors for service \").concat(serviceId || \"ALL\"));\n            }\n        } catch (err) {\n            console.error(\"Error loading doctors:\", err);\n            setError(\"Error al cargar doctores\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAvailability = async ()=>{\n        if (!formData.appointment_date) return;\n        try {\n            setLoading(true);\n            let url = \"/api/doctors/availability?organizationId=\".concat(organizationId, \"&date=\").concat(formData.appointment_date);\n            if (formData.doctor_id) {\n                url += \"&doctorId=\".concat(formData.doctor_id);\n            }\n            if (formData.service_id) {\n                url += \"&serviceId=\".concat(formData.service_id);\n            }\n            if (formData.location_id) {\n                url += \"&locationId=\".concat(formData.location_id);\n            }\n            const response = await fetch(url);\n            if (response.ok) {\n                const data = await response.json();\n                setAvailability(data.data || []);\n            }\n        } catch (err) {\n            console.error(\"Error loading availability:\", err);\n            setError(\"Error al cargar disponibilidad\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced availability loader for WeeklyAvailabilitySelector\n    const loadWeeklyAvailability = async (params)=>{\n        try {\n            let url = \"/api/appointments/availability?organizationId=\".concat(params.organizationId, \"&startDate=\").concat(params.startDate, \"&endDate=\").concat(params.endDate);\n            if (params.serviceId) url += \"&serviceId=\".concat(params.serviceId);\n            if (params.doctorId) url += \"&doctorId=\".concat(params.doctorId);\n            if (params.locationId) url += \"&locationId=\".concat(params.locationId);\n            const response = await fetch(url);\n            if (!response.ok) {\n                throw new Error(\"Error \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error || \"Error desconocido al cargar disponibilidad\");\n            }\n            // Process API response into DayAvailabilityData format\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            const today = new Date();\n            const tomorrow = new Date(today);\n            tomorrow.setDate(today.getDate() + 1);\n            const processedData = [];\n            const startDate = new Date(params.startDate);\n            const endDate = new Date(params.endDate);\n            for(let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)){\n                const dateString = date.toISOString().split(\"T\")[0];\n                const dayData = result.data[dateString];\n                const availableSlots = (dayData === null || dayData === void 0 ? void 0 : dayData.availableSlots) || 0;\n                const isToday = date.toDateString() === today.toDateString();\n                const isTomorrow = date.toDateString() === tomorrow.toDateString();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                let availabilityLevel = \"none\";\n                if (availableSlots === 0) availabilityLevel = \"none\";\n                else if (availableSlots <= 2) availabilityLevel = \"low\";\n                else if (availableSlots <= 5) availabilityLevel = \"medium\";\n                else availabilityLevel = \"high\";\n                processedData.push({\n                    date: dateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount: availableSlots,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    slots: (dayData === null || dayData === void 0 ? void 0 : dayData.slots) || []\n                });\n            }\n            return processedData;\n        } catch (error) {\n            console.error(\"Error loading weekly availability:\", error);\n            throw error;\n        }\n    };\n    // Navigation handlers\n    const handleNext = ()=>{\n        const steps = getSteps();\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n            setError(null);\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            const newStep = currentStep - 1;\n            setCurrentStep(newStep);\n            setError(null);\n            // CRITICAL FIX: Reset bookingFlow when returning to flow selection step\n            const steps = getSteps();\n            const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n            if (newStep === flowStepIndex) {\n                console.log(\"DEBUG: Resetting bookingFlow state for flow selection step\");\n                setBookingFlow(null);\n                setOptimalAppointment(null);\n                setIsSearchingOptimal(false);\n                // Clear doctors to force reload when flow is selected again\n                setDoctors([]);\n            }\n        }\n    };\n    const handleCancel = ()=>{\n        setShowCancelDialog(true);\n    };\n    const handleConfirmCancel = ()=>{\n        setShowCancelDialog(false);\n        onCancel === null || onCancel === void 0 ? void 0 : onCancel();\n    };\n    const handleCancelDialog = ()=>{\n        setShowCancelDialog(false);\n    };\n    // Selection handlers\n    const handleServiceSelect = (option)=>{\n        setFormData((prev)=>({\n                ...prev,\n                service_id: option.id\n            }));\n        // Don't load doctors yet - wait for flow selection\n        handleNext();\n    };\n    const handleDoctorSelect = (option)=>{\n        setFormData((prev)=>({\n                ...prev,\n                doctor_id: option.id\n            }));\n        handleNext();\n    };\n    const handleLocationSelect = (option)=>{\n        setFormData((prev)=>({\n                ...prev,\n                location_id: option.id\n            }));\n        handleNext();\n    };\n    const handleDateSelect = (date)=>{\n        setFormData((prev)=>({\n                ...prev,\n                appointment_date: date,\n                appointment_time: \"\"\n            }));\n        setSelectedSlot(null);\n        handleNext();\n    };\n    const handleSlotSelect = (slot)=>{\n        setSelectedSlot(slot);\n        setFormData((prev)=>({\n                ...prev,\n                appointment_time: slot.start_time,\n                doctor_id: slot.doctor_id // Auto-assign doctor if not selected\n            }));\n        handleNext();\n    };\n    // Hybrid flow handlers\n    const handleFlowSelect = async (flowType)=>{\n        setBookingFlow(flowType);\n        if (flowType === \"express\") {\n            // Show searching state and find optimal appointment\n            setIsSearchingOptimal(true);\n            handleNext(); // Move to searching step\n            await findOptimalAppointment();\n        } else {\n            // Continue with personalized flow\n            loadDoctors(formData.service_id);\n            handleNext();\n        }\n    };\n    const findOptimalAppointment = async ()=>{\n        if (!formData.service_id) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // Add minimum delay to show searching animation\n            const searchPromise = new _lib_appointments_OptimalAppointmentFinder__WEBPACK_IMPORTED_MODULE_8__.OptimalAppointmentFinder().findOptimalAppointment({\n                serviceId: formData.service_id,\n                organizationId,\n                preferences: {\n                    maxDaysOut: 14,\n                    timePreference: \"any\"\n                }\n            });\n            // Ensure minimum 8 seconds for UX (matches ExpressSearchingState animation)\n            const [result] = await Promise.all([\n                searchPromise,\n                new Promise((resolve)=>setTimeout(resolve, 8000))\n            ]);\n            if (result) {\n                setOptimalAppointment(result);\n                // Update form data with optimal appointment\n                setFormData((prev)=>({\n                        ...prev,\n                        doctor_id: result.appointment.doctorId,\n                        location_id: result.appointment.locationId,\n                        appointment_date: result.appointment.date,\n                        appointment_time: result.appointment.startTime\n                    }));\n                setIsSearchingOptimal(false);\n                // Skip to confirmation step (step 2 in express flow)\n                setCurrentStep(2);\n            } else {\n                setIsSearchingOptimal(false);\n                setError(\"No se encontraron citas disponibles. Intenta con la reserva personalizada.\");\n                setBookingFlow(\"personalized\");\n                loadDoctors(formData.service_id);\n                setCurrentStep(2); // Go to doctor selection in personalized flow\n            }\n        } catch (err) {\n            console.error(\"Error finding optimal appointment:\", err);\n            setIsSearchingOptimal(false);\n            setError(\"Error al buscar citas disponibles. Intenta con la reserva personalizada.\");\n            setBookingFlow(\"personalized\");\n            loadDoctors(formData.service_id);\n            setCurrentStep(2); // Go to doctor selection in personalized flow\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExpressConfirm = async ()=>{\n        if (!optimalAppointment || !userId) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const bookingData = {\n                organizationId,\n                patientId: userId,\n                doctorId: optimalAppointment.appointment.doctorId,\n                serviceId: formData.service_id,\n                locationId: optimalAppointment.appointment.locationId,\n                appointmentDate: optimalAppointment.appointment.date,\n                startTime: optimalAppointment.appointment.startTime,\n                endTime: optimalAppointment.appointment.endTime,\n                reason: formData.reason,\n                notes: formData.notes || \"Cita agendada via \".concat(mode === \"ai\" ? \"AI Assistant\" : \"Reserva Express\")\n            };\n            const response = await fetch(\"/api/appointments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(bookingData)\n            });\n            const result = await response.json();\n            if (result.success || response.ok) {\n                var _result_data;\n                onAppointmentBooked === null || onAppointmentBooked === void 0 ? void 0 : onAppointmentBooked(result.appointmentId || ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.id));\n            } else {\n                throw new Error(result.error || \"Failed to create appointment\");\n            }\n        } catch (err) {\n            console.error(\"Error booking express appointment:\", err);\n            setError(err instanceof Error ? err.message : \"Error al agendar la cita\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCustomizeFromExpress = ()=>{\n        setBookingFlow(\"personalized\");\n        setOptimalAppointment(null);\n        setIsSearchingOptimal(false);\n        loadDoctors(formData.service_id);\n        setCurrentStep(2); // Go directly to doctor selection in personalized flow\n    };\n    const handleBackToFlowSelection = ()=>{\n        setBookingFlow(null);\n        setOptimalAppointment(null);\n        setIsSearchingOptimal(false);\n        setCurrentStep(1); // Go back to flow selection\n    };\n    const handleCancelExpressSearch = ()=>{\n        setIsSearchingOptimal(false);\n        setBookingFlow(\"personalized\");\n        loadDoctors(formData.service_id);\n        setCurrentStep(2); // Go to doctor selection in personalized flow\n    };\n    // Load availability when date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const steps = getSteps();\n        const timeStepIndex = steps.findIndex((step)=>step.id === \"time\");\n        if (formData.appointment_date && currentStep === timeStepIndex) {\n            loadAvailability();\n        }\n    }, [\n        formData.appointment_date,\n        currentStep,\n        bookingFlow\n    ]);\n    // CRITICAL FIX: Validate state consistency to prevent navigation bugs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const steps = getSteps();\n        const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n        // If we're on flow selection step but bookingFlow is already set, reset it\n        if (currentStep === flowStepIndex && bookingFlow) {\n            console.warn(\"DEBUG: Inconsistent state detected - resetting bookingFlow on flow step\");\n            setBookingFlow(null);\n            setOptimalAppointment(null);\n            setIsSearchingOptimal(false);\n        }\n    }, [\n        currentStep,\n        bookingFlow\n    ]);\n    // Handle appointment booking\n    const handleBookAppointment = async ()=>{\n        if (!selectedSlot || !userId) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const bookingData = {\n                organizationId,\n                patientId: userId,\n                doctorId: selectedSlot.doctor_id,\n                serviceId: formData.service_id,\n                locationId: formData.location_id,\n                appointmentDate: formData.appointment_date,\n                startTime: selectedSlot.start_time,\n                endTime: selectedSlot.end_time,\n                reason: formData.reason,\n                notes: formData.notes || \"Cita agendada via \".concat(mode === \"ai\" ? \"AI Assistant\" : \"formulario manual\")\n            };\n            const response = await fetch(\"/api/appointments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(bookingData)\n            });\n            const result = await response.json();\n            if (result.success || response.ok) {\n                var _result_data;\n                onAppointmentBooked === null || onAppointmentBooked === void 0 ? void 0 : onAppointmentBooked(result.appointmentId || ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.id));\n            } else {\n                throw new Error(result.error || \"Failed to create appointment\");\n            }\n        } catch (err) {\n            console.error(\"Error booking appointment:\", err);\n            setError(err instanceof Error ? err.message : \"Error al agendar la cita\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg max-w-2xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.ProgressIndicator, {\n                        steps: updateSteps(),\n                        currentStep: currentStep\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.AlertMessage, {\n                                type: \"error\",\n                                title: \"Error\",\n                                message: error,\n                                onDismiss: ()=>setError(null),\n                                className: \"mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 11\n                            }, this),\n                            currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.SelectionCard, {\n                                title: \"\\xbfQu\\xe9 tipo de consulta necesitas?\",\n                                subtitle: \"Selecciona el servicio m\\xe9dico que requieres\",\n                                options: services.map((service)=>({\n                                        id: service.id,\n                                        title: service.name,\n                                        description: service.description,\n                                        price: service.price\n                                    })),\n                                selectedId: formData.service_id,\n                                onSelect: handleServiceSelect,\n                                loading: loading,\n                                emptyMessage: \"No hay servicios disponibles\",\n                                gridCols: 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            (()=>{\n                                const steps = getSteps();\n                                const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n                                return currentStep === flowStepIndex && !bookingFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlowSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onFlowSelect: handleFlowSelect,\n                                    loading: loading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const flowStepIndex = steps.findIndex((step)=>step.id === \"flow\");\n                                return currentStep === flowStepIndex + 1 && bookingFlow === \"express\" && isSearchingOptimal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpressSearchingState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    onCancel: handleCancelExpressSearch\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const doctorStepIndex = steps.findIndex((step)=>step.id === \"doctor\");\n                                return currentStep === doctorStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.SelectionCard, {\n                                    title: \"\\xbfTienes preferencia por alg\\xfan doctor?\",\n                                    subtitle: \"Puedes elegir un doctor espec\\xedfico o continuar para ver disponibilidad general\",\n                                    options: [\n                                        {\n                                            id: \"\",\n                                            title: \"Cualquier doctor disponible\",\n                                            description: \"Ver disponibilidad de todos los doctores (\".concat(doctors.length, \" disponibles)\"),\n                                            subtitle: \"Recomendado para mayor flexibilidad de horarios\"\n                                        },\n                                        ...doctors.map((doctor)=>({\n                                                id: doctor.id,\n                                                title: \"Dr. \".concat(doctor.profiles.first_name, \" \").concat(doctor.profiles.last_name),\n                                                subtitle: doctor.specialization,\n                                                price: doctor.consultation_fee\n                                            }))\n                                    ],\n                                    selectedId: formData.doctor_id,\n                                    onSelect: handleDoctorSelect,\n                                    loading: loading,\n                                    emptyMessage: \"No hay doctores disponibles\",\n                                    gridCols: 1\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const locationStepIndex = steps.findIndex((step)=>step.id === \"location\");\n                                return currentStep === locationStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.SelectionCard, {\n                                    title: \"\\xbfEn qu\\xe9 sede prefieres la consulta?\",\n                                    subtitle: \"Selecciona la ubicaci\\xf3n m\\xe1s conveniente para ti\",\n                                    options: [\n                                        {\n                                            id: \"\",\n                                            title: \"Cualquier sede disponible\",\n                                            description: \"Ver disponibilidad en todas las sedes (\".concat(locations.length, \" disponibles)\"),\n                                            subtitle: \"Recomendado para mayor flexibilidad de horarios\"\n                                        },\n                                        ...locations.map((location)=>({\n                                                id: location.id,\n                                                title: location.name,\n                                                description: location.address\n                                            }))\n                                    ],\n                                    selectedId: formData.location_id,\n                                    onSelect: handleLocationSelect,\n                                    loading: loading,\n                                    emptyMessage: \"No hay sedes disponibles\",\n                                    gridCols: 1\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const dateStepIndex = steps.findIndex((step)=>step.id === \"date\");\n                                return currentStep === dateStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyAvailabilitySelector__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    title: \"\\xbfCu\\xe1ndo te gustar\\xeda la cita?\",\n                                    subtitle: \"Selecciona la fecha que mejor te convenga\",\n                                    selectedDate: formData.appointment_date,\n                                    onDateSelect: handleDateSelect,\n                                    organizationId: organizationId,\n                                    serviceId: formData.service_id,\n                                    doctorId: formData.doctor_id,\n                                    locationId: formData.location_id,\n                                    minDate: (()=>{\n                                        // CRITICAL FIX: Use timezone-safe date formatting to prevent UTC conversion issues\n                                        const now = new Date();\n                                        return \"\".concat(now.getFullYear(), \"-\").concat(String(now.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(now.getDate()).padStart(2, \"0\"));\n                                    })(),\n                                    showDensityIndicators: true,\n                                    enableSmartSuggestions: mode === \"ai\" && !!aiContext,\n                                    aiContext: aiContext || undefined,\n                                    entryMode: mode,\n                                    onLoadAvailability: loadWeeklyAvailability,\n                                    loading: loading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const timeStepIndex = steps.findIndex((step)=>step.id === \"time\");\n                                return currentStep === timeStepIndex && bookingFlow === \"personalized\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedTimeSlotSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    title: \"Horarios disponibles para \".concat(formData.appointment_date),\n                                    subtitle: \"Selecciona el horario que prefieras\",\n                                    slots: availability,\n                                    selectedSlot: selectedSlot,\n                                    onSlotSelect: handleSlotSelect,\n                                    loading: loading,\n                                    emptyMessage: \"No hay horarios disponibles para esta fecha. Intenta con otra fecha.\",\n                                    showDoctorInfo: !formData.doctor_id,\n                                    showPricing: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                const steps = getSteps();\n                                const confirmStepIndex = steps.findIndex((step)=>step.id === \"confirm\");\n                                return currentStep === confirmStepIndex && bookingFlow === \"express\" && optimalAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpressConfirmation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    appointment: optimalAppointment,\n                                    onConfirm: handleExpressConfirm,\n                                    onCustomize: handleCustomizeFromExpress,\n                                    onBack: handleBackToFlowSelection,\n                                    loading: loading,\n                                    reason: formData.reason,\n                                    notes: formData.notes,\n                                    patientName: patientName,\n                                    onReasonChange: (reason)=>setFormData((prev)=>({\n                                                ...prev,\n                                                reason\n                                            })),\n                                    onNotesChange: (notes)=>setFormData((prev)=>({\n                                                ...prev,\n                                                notes\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 13\n                                }, this);\n                            })(),\n                            (()=>{\n                                var _services_find, _locations_find;\n                                const steps = getSteps();\n                                const confirmStepIndex = steps.findIndex((step)=>step.id === \"confirm\");\n                                return currentStep === confirmStepIndex && bookingFlow === \"personalized\" && selectedSlot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border-2 border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                    children: \"Confirmar tu cita\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Revisa los detalles y confirma tu cita m\\xe9dica\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.AppointmentSummary, {\n                                            service: (_services_find = services.find((s)=>s.id === formData.service_id)) === null || _services_find === void 0 ? void 0 : _services_find.name,\n                                            doctor: selectedSlot.doctor_name,\n                                            location: (_locations_find = locations.find((l)=>l.id === formData.location_id)) === null || _locations_find === void 0 ? void 0 : _locations_find.name,\n                                            date: formData.appointment_date,\n                                            time: formData.appointment_time,\n                                            specialization: selectedSlot.specialization,\n                                            price: selectedSlot.consultation_fee,\n                                            reason: formData.reason,\n                                            notes: formData.notes,\n                                            patientName: patientName,\n                                            className: \"mb-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Motivo de la consulta (opcional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.reason,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        reason: e.target.value\n                                                                    })),\n                                                            className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Ej: Consulta general, dolor de cabeza, etc.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Notas adicionales (opcional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.notes,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        notes: e.target.value\n                                                                    })),\n                                                            rows: 3,\n                                                            className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Informaci\\xf3n adicional que consideres importante...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleBookAppointment,\n                                                    disabled: loading,\n                                                    className: \"w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 transition-all duration-300 flex items-center justify-center text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02]\",\n                                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Agendando tu cita...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 mr-2\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Confirmar Cita\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-3\",\n                                                    children: \"Al confirmar, recibir\\xe1s un email con los detalles de tu cita\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 13\n                                }, this);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 px-6 py-4 flex justify-between items-center rounded-b-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    currentStep > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleBack,\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors px-3 py-2 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Anterior\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleCancel,\n                                        className: \"flex items-center text-red-600 hover:text-red-700 transition-colors px-3 py-2 rounded-lg hover:bg-red-50 border border-red-200 hover:border-red-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-4 w-4 mr-1\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500 font-medium\",\n                                children: [\n                                    \"Paso \",\n                                    currentStep + 1,\n                                    \" de \",\n                                    getSteps().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 9\n                            }, this),\n                            currentStep < getSteps().length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleNext,\n                                className: \"flex items-center text-blue-600 hover:text-blue-700 transition-colors px-4 py-2 rounded-lg hover:bg-blue-50 border border-blue-200 hover:border-blue-300 font-medium\",\n                                children: [\n                                    \"Siguiente\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                        lineNumber: 843,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared__WEBPACK_IMPORTED_MODULE_2__.ConfirmationDialog, {\n                isOpen: showCancelDialog,\n                title: \"Cancelar reserva de cita\",\n                message: \"\\xbfEst\\xe1s seguro de que quieres cancelar? Se perder\\xe1 toda la informaci\\xf3n ingresada y tendr\\xe1s que empezar de nuevo.\",\n                confirmText: \"S\\xed, cancelar\",\n                cancelText: \"Continuar reservando\",\n                confirmVariant: \"danger\",\n                onConfirm: handleConfirmCancel,\n                onCancel: handleCancelDialog\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\UnifiedAppointmentFlow.tsx\",\n                lineNumber: 885,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UnifiedAppointmentFlow, \"nsGTRSDWYbMPpSOHJyh60epgN2o=\");\n_c = UnifiedAppointmentFlow;\nvar _c;\n$RefreshReg$(_c, \"UnifiedAppointmentFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/UnifiedAppointmentFlow.tsx\n"));

/***/ })

});