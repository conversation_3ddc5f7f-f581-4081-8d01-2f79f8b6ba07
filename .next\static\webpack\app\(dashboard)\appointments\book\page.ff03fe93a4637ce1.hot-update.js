"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // Crear Date object y analizar timezone issues\n        const dateObj = new Date(date);\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        console.log(\"Date object creado:\", dateObj);\n        console.log(\"Date object ISO (UTC):\", dateObj.toISOString());\n        console.log(\"Date object local string:\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic\n        const utcDateString = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateString;\n        console.log(\"Comparaci\\xf3n timezone:\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString:\", utcDateString);\n        console.log(\"  - localDateString:\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 505,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 515,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 528,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 567,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 595,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 603,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: weekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 621,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 662,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 503,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"4O/41eNoaHqwK0PAWV/sHVC2lvE=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});