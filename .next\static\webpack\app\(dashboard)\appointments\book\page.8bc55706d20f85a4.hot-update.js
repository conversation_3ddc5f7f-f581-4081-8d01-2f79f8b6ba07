"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/appointments/book/page",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* harmony import */ var _lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/dateValidation */ \"(app-pages-browser)/./src/lib/utils/dateValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // Si es fecha pasada, no mostrar slots disponibles\n                let slotsCount = 0;\n                if (!isPastDate) {\n                    slotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                }\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                } else if (slotsCount === 0) {\n                    availabilityLevel = \"none\";\n                } else if (slotsCount <= 2) {\n                    availabilityLevel = \"low\";\n                } else if (slotsCount <= 5) {\n                    availabilityLevel = \"medium\";\n                } else {\n                    availabilityLevel = \"high\";\n                }\n                // CRITICAL FIX: Use timezone-safe date formatting\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            setWeekData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * CRITICAL FEATURE: UI-level date blocking validation\n   * Validates which dates should be blocked based on 4-hour advance booking rule\n   * Prevents user confusion by showing blocked dates as disabled\n   */ const dateValidationResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (weekData.length === 0) return {};\n        const dates = weekData.map((day)=>day.date);\n        // Create mock time slots for validation (typical business hours)\n        const availableSlotsByDate = {};\n        weekData.forEach((day)=>{\n            if (day.availabilityLevel !== \"none\") {\n                // Generate typical business hours for validation\n                availableSlotsByDate[day.date] = [\n                    {\n                        date: day.date,\n                        time: \"08:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"10:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"11:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"14:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"15:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"16:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"17:00\",\n                        available: true\n                    },\n                    {\n                        date: day.date,\n                        time: \"18:00\",\n                        available: true\n                    }\n                ];\n            } else {\n                availableSlotsByDate[day.date] = [];\n            }\n        });\n        console.log(\"=== DEBUG DATE BLOCKING VALIDATION ===\");\n        console.log(\"Validating dates:\", dates);\n        console.log(\"Available slots by date:\", availableSlotsByDate);\n        const validationResults = (0,_lib_utils_dateValidation__WEBPACK_IMPORTED_MODULE_5__.validateMultipleDates)(dates, availableSlotsByDate);\n        console.log(\"Validation results:\", validationResults);\n        console.log(\"========================================\");\n        return validationResults;\n    }, [\n        weekData\n    ]);\n    /**\n   * Enhanced week data with blocking information\n   */ const enhancedWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return weekData.map((day)=>{\n            const validation = dateValidationResults[day.date];\n            const isBlocked = validation && !validation.isValid;\n            return {\n                ...day,\n                isBlocked,\n                blockReason: validation === null || validation === void 0 ? void 0 : validation.reason,\n                validationResult: validation\n            };\n        });\n    }, [\n        weekData,\n        dateValidationResults\n    ]);\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha con validación de bloqueo\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE + BLOCKING) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FEATURE: Check if date is blocked by UI validation\n        const validation = dateValidationResults[date];\n        const isBlocked = validation && !validation.isValid;\n        console.log(\"Validaci\\xf3n de bloqueo:\");\n        console.log(\"  - validation:\", validation);\n        console.log(\"  - isBlocked:\", isBlocked);\n        console.log(\"  - blockReason:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n        if (isBlocked) {\n            console.log(\"\\uD83D\\uDEAB FECHA BLOQUEADA - No se permite selecci\\xf3n\");\n            console.log(\"Raz\\xf3n:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n            console.log(\"=======================================\");\n            // Show user feedback (could be enhanced with toast notification)\n            alert(\"Esta fecha no est\\xe1 disponible: \".concat(validation === null || validation === void 0 ? void 0 : validation.reason));\n            return;\n        }\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic using timezone-safe objects\n        const utcDateStringFromUTC = dateObjUTC.toISOString().split(\"T\")[0];\n        const utcDateStringFromLocal = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateStringFromUTC;\n        console.log(\"Comparaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString (from UTC obj):\", utcDateStringFromUTC);\n        console.log(\"  - utcDateString (from local obj):\", utcDateStringFromLocal);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"\\xbfDate objects son consistentes?:\", localDateString === date);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"✅ FECHA V\\xc1LIDA - LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 596,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 606,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 619,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 649,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 686,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 699,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 694,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && enhancedWeekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: enhancedWeekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount,\n                            isBlocked: day.isBlocked,\n                            blockReason: day.blockReason\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 713,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 712,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 730,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 755,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 594,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"14U+tOJsF/03vp4I7+MnLlHkFdQ=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});