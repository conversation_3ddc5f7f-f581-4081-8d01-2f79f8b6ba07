{"node": {"09c3d9db0ebdf96d1a679578bc84c7d7074d18e5": {"workers": {"app/(dashboard)/appointments/page": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Capi%5C%5Cappointments%5C%5Cactions.ts%22%2C%5B%22updateAppointment%22%2C%22createAppointment%22%2C%22getAvailableSlots%22%2C%22upsertDoctorSchedule%22%2C%22cancelAppointment%22%5D%5D%5D&__client_imported__=true!"}, "layer": {"app/(dashboard)/appointments/page": "action-browser"}}, "1296067d185b0f719ebff967ae8480f0ba7f19a1": {"workers": {"app/(dashboard)/appointments/page": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Capi%5C%5Cappointments%5C%5Cactions.ts%22%2C%5B%22updateAppointment%22%2C%22createAppointment%22%2C%22getAvailableSlots%22%2C%22upsertDoctorSchedule%22%2C%22cancelAppointment%22%5D%5D%5D&__client_imported__=true!"}, "layer": {"app/(dashboard)/appointments/page": "action-browser"}}, "2f01e619bcad803ec119c5e178cefee18371968f": {"workers": {"app/(dashboard)/appointments/page": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Capi%5C%5Cappointments%5C%5Cactions.ts%22%2C%5B%22updateAppointment%22%2C%22createAppointment%22%2C%22getAvailableSlots%22%2C%22upsertDoctorSchedule%22%2C%22cancelAppointment%22%5D%5D%5D&__client_imported__=true!"}, "layer": {"app/(dashboard)/appointments/page": "action-browser"}}, "a4567922d47b5da1a524ff9c61540db7357e43c8": {"workers": {"app/(dashboard)/appointments/page": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Capi%5C%5Cappointments%5C%5Cactions.ts%22%2C%5B%22updateAppointment%22%2C%22createAppointment%22%2C%22getAvailableSlots%22%2C%22upsertDoctorSchedule%22%2C%22cancelAppointment%22%5D%5D%5D&__client_imported__=true!"}, "layer": {"app/(dashboard)/appointments/page": "action-browser"}}, "b75dd3ea132b9c5958d537ef66dffba6deeb57fd": {"workers": {"app/(dashboard)/appointments/page": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagensalud-sonnet4%5C%5Csrc%5C%5Capp%5C%5Capi%5C%5Cappointments%5C%5Cactions.ts%22%2C%5B%22updateAppointment%22%2C%22createAppointment%22%2C%22getAvailableSlots%22%2C%22upsertDoctorSchedule%22%2C%22cancelAppointment%22%5D%5D%5D&__client_imported__=true!"}, "layer": {"app/(dashboard)/appointments/page": "action-browser"}}}, "edge": {}, "encryptionKey": "HZ/jQ2DMS56G/RH3SeeE8kN0ni3C9tg5hW9m6ijhIu4="}